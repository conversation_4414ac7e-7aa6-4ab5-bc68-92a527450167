using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
    /// ModuleUtilitiesAPI Component
    /// Web API bridge for ModuleUtilities functionality
    /// </summary>
    public partial class ModuleUtilitiesAPI : BaseServerComponent, IModuleUtilitiesAPI
    {
        private readonly IModuleUtilities _moduleUtilities;

        public ModuleUtilitiesAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, IModuleUtilities moduleUtilities) 
            : base(serviceProvider, configuration, dataFacade)
        {
            _moduleUtilities = moduleUtilities;
        }

        /// <summary>
        /// GetAvailableModules API Method
        /// Web API endpoint for getting available modules with search support
        /// </summary>
        /// <param name="dealerId">Dealer ID to filter modules</param>
        /// <param name="filterPredicate">Search filter predicate</param>
        /// <param name="filterParameters">Search filter parameters</param>
        /// <param name="parameters">Additional parameters dictionary</param>
        /// <returns>Collection of available modules</returns>
        public async Task<ComponentResponse<DataObjectCollection<ModuleDataObject>>> GetAvailableModulesAsync(
            Guid? dealerId = null, 
            string filterPredicate = null, 
            string filterParameters = null, 
            Dictionary<string, object> parameters = null)
        {
            // Create parameters dictionary to pass search data
            var searchParameters = parameters ?? new Dictionary<string, object>();

            // Add search parameters if provided
            if (!string.IsNullOrEmpty(filterPredicate))
            {
                searchParameters["filterPredicate"] = filterPredicate;
            }

            if (!string.IsNullOrEmpty(filterParameters))
            {
                searchParameters["filterParameters"] = filterParameters;
            }

            // Log for debugging
            System.Diagnostics.Debug.WriteLine($"[API] ModuleUtilitiesAPI.GetAvailableModulesAsync called with dealerId: {dealerId}");
            System.Diagnostics.Debug.WriteLine($"[API] filterPredicate: '{filterPredicate}'");
            System.Diagnostics.Debug.WriteLine($"[API] filterParameters: '{filterParameters}'");
            System.Diagnostics.Debug.WriteLine($"[API] parameters count: {searchParameters?.Count ?? 0}");

            // Call the underlying ModuleUtilities component
            return await _moduleUtilities.GetAvailableModulesAsync(dealerId ?? Guid.Empty, searchParameters);
        }

        /// <summary>
        /// ResetCalibration API Method
        /// Web API endpoint for resetting module calibration
        /// </summary>
        /// <param name="moduleId">Module ID to reset calibration for</param>
        /// <param name="parameters">Additional parameters</param>
        /// <returns>Updated module data object</returns>
        public async Task<ComponentResponse<ModuleDataObject>> ResetCalibrationAsync(Guid moduleId, Dictionary<string, object> parameters = null)
        {
            return await _moduleUtilities.ResetCalibrationAsync(moduleId, parameters);
        }

        /// <summary>
        /// SwapModuleForVehicle API Method
        /// Web API endpoint for swapping modules between vehicles
        /// </summary>
        /// <param name="vehicleId">Vehicle ID</param>
        /// <param name="newModuleId">New module ID</param>
        /// <param name="note">Swap note</param>
        /// <param name="parameters">Additional parameters</param>
        /// <returns>Success status</returns>
        public async Task<ComponentResponse<System.Boolean>> SwapModuleForVehicleAsync(
            System.Guid vehicleId, 
            System.Guid newModuleId, 
            System.String note, 
            Dictionary<string, object> parameters = null)
        {
            return await _moduleUtilities.SwapModuleForVehicleAsync(vehicleId, newModuleId, note, parameters);
        }
    }
}