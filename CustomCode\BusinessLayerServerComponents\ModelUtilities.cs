using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// ModelUtilities Component
	///  
	/// </summary>
    public partial class ModelUtilities : BaseServerComponent, IModelUtilities 
    {
		public ModelUtilities(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
		{
		}

        public async System.Threading.Tasks.Task<ComponentResponse<bool>> ApplyCategoryAsync(Guid modelId, Guid[] customerIds, Dictionary<string, object> parameters = null)
        {
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = modelId;

            model = await _dataFacade.ModelDataProvider.GetAsync(model, skipSecurity: true);

            if (model == null)
            {
                return new ComponentResponse<bool>(false);
            }

            foreach (var customerId in customerIds) 
            {
                var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
                customer.Id = customerId;

                customer = await _dataFacade.CustomerDataProvider.GetAsync(customer, skipSecurity: true);

                if (customer == null)
                {
                    continue;
                }

                var customerModels = await customer.LoadCustomerModelItemsAsync();

                var existingCustomerModel = customerModels.FirstOrDefault(x => x.ModelId == modelId);

                if (existingCustomerModel == null)
                {
                    existingCustomerModel = _serviceProvider.GetRequiredService<CustomerModelDataObject>();
                    existingCustomerModel.ModelId = modelId;
                    existingCustomerModel.CustomerId = customerId;

                    existingCustomerModel = await _dataFacade.CustomerModelDataProvider.SaveAsync(existingCustomerModel);
                }
            }

            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<DataObjectCollection<ModelDataObject>>> GetAvailableModelsAsync(Guid customerId, Dictionary<string, object> parameters = null)
        {
            if (customerId == Guid.Empty)
            {
                return new ComponentResponse<DataObjectCollection<ModelDataObject>>(new DataObjectCollection<ModelDataObject>());
            }

            // Get all CustomerModel entries for this customer in one query
            var customerModels = await _dataFacade.CustomerModelDataProvider.GetCollectionAsync(
                null,
                "CustomerId == @0",
                new object[] { customerId });

            // Extract all model IDs
            var modelIds = customerModels.Select(cm => cm.ModelId).ToArray();

            if (!modelIds.Any())
            {
                return new ComponentResponse<DataObjectCollection<ModelDataObject>>(new DataObjectCollection<ModelDataObject>());
            }

            // Build dynamic OR conditions for each model ID
            var conditions = new List<string>();
            var parametersList = new List<object>();
            
            for (int i = 0; i < modelIds.Length; i++)
            {
                conditions.Add($"Id == @{i}");
                parametersList.Add(modelIds[i]);
            }

            // Combine conditions with OR
            var filterPredicate = string.Join(" || ", conditions);

            // Get all models in a single query
            var models = await _dataFacade.ModelDataProvider.GetCollectionAsync(
                null,
                filterPredicate,
                parametersList.ToArray());

            return new ComponentResponse<DataObjectCollection<ModelDataObject>>(models);
        }
    }
}
