(function () {
    // Custom code for VehilceForm1ViewModel
    FleetXQ.Web.ViewModels.VehilceForm1ViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        this.initialize = function () {
            // Override getModuleCollectionData to add dealerId from customer
            self.viewmodel.getModuleCollectionData = function (callback) {
                self.viewmodel.isGetModuleCollectionBusy(true);

                var configuration = {};
                configuration.contextId = self.viewmodel.ModuleContextId;
                configuration.filterPredicate = "";

                // Get dealerId from customer if available
                var customer = self.viewmodel.VehicleObject().getCustomer();
                if (customer && customer.Data.DealerId) {
                    configuration.dealerId = customer.Data.DealerId();
                }

                configuration.successHandler = callback || self.viewmodel.onGetModuleCollectionDataSuccess;

                configuration.errorHandler = self.viewmodel.onGetModuleCollectionDataError;

                self.viewmodel.controller.applicationController.getProxyForComponent("ModuleUtilities").GetAvailableModules(configuration);
            };

            // Override getFilteredModuleCollectionData to add dealerId from customer
            self.viewmodel.getFilteredModuleCollectionData = function (searchValue, callback) {
                self.viewmodel.isGetModuleCollectionBusy(true);

                var configuration = {};
                configuration.contextId = self.viewmodel.ModuleContextId;
                configuration.filterPredicate = 'IoTDevice.Contains(@0)';
                configuration.filterParameters = JSON.stringify([{
                    "TypeName": "System.String",
                    "IsNullable": false,
                    "Value": searchValue
                }]);
                configuration.pageSize = 50;
                configuration.pageNumber = 1;

                // Get dealerId from customer if available
                var customer = self.viewmodel.VehicleObject().getCustomer();
                if (customer && customer.Data.DealerId) {
                    configuration.dealerId = customer.Data.DealerId();
                }

                configuration.successHandler = function (data) {
                    self.viewmodel.isGetModuleCollectionBusy(false);
                    if (callback) callback(data);
                };

                configuration.errorHandler = function (error) {
                    self.viewmodel.isGetModuleCollectionBusy(false);
                    self.viewmodel.onGetModuleCollectionDataError(error);
                };

                self.viewmodel.controller.applicationController.getProxyForComponent("ModuleUtilities").GetAvailableModules(configuration);
            };
        };
    }
}()); 