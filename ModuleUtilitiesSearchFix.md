# ModuleUtilities Search Functionality Fix

## Problem
The `GetAvailableModulesAsync` method in `ModuleUtilities.cs` was ignoring search parameters passed from the client-side, causing all searches to return the same results regardless of the search keyword.

## Root Cause
The original implementation only filtered modules by status and dealer ID, but completely ignored any search parameters passed in the `parameters` dictionary.

## Solution
Modified the `GetAvailableModulesAsync` method to:

1. **Extract search parameters** from the `parameters` dictionary:
   - `filterPredicate`: Contains the search expression (e.g., `"IoTDevice.Contains(@0)"`)
   - `filterParameters`: Contains the search values as JSON (e.g., `"[{\"TypeName\":\"System.String\",\"Value\":\"device123\"}]"`)

2. **Parse JSON parameters** safely with error handling to prevent crashes from invalid JSON

3. **Adjust parameter indices** to account for existing status and dealer ID parameters in the query

4. **Combine filters** to create a final query that filters by status, dealer ID, AND search criteria

## Implementation Details

### Before (Broken)
```csharp
// Only filtered by status and dealer - ignored search parameters
var queryFilter = "(Status = @0 OR Status = null) AND Vehicle = null";
object[] queryParameters = new object[] { (int)ModuleStatusEnum.Spare };

if (dealerId != Guid.Empty)
{
    queryFilter += " AND DealerId = @1";
    queryParameters = new object[] { (int)ModuleStatusEnum.Spare, dealerId };
}

var result = await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, queryFilter, queryParameters);
```

### After (Fixed)
```csharp
// Extract and process search parameters
string searchFilterPredicate = null;
object[] searchFilterParameters = null;

if (parameters != null)
{
    if (parameters.TryGetValue("filterPredicate", out var filterPredicateValue))
        searchFilterPredicate = filterPredicateValue?.ToString();
    
    if (parameters.TryGetValue("filterParameters", out var filterParametersValue))
    {
        // Parse JSON parameters safely
        var filterParams = JsonSerializer.Deserialize<List<Dictionary<string, object>>>(json);
        searchFilterParameters = filterParams?.Select(p => p.TryGetValue("Value", out var value) ? value : null).ToArray();
    }
}

// Combine base filter with search filter
if (!string.IsNullOrEmpty(searchFilterPredicate))
{
    // Adjust parameter indices for search parameters
    var adjustedSearchPredicate = AdjustParameterIndices(searchFilterPredicate, parameterIndex);
    queryFilter = $"({queryFilter}) AND ({adjustedSearchPredicate})";
    queryParametersList.AddRange(searchFilterParameters ?? Array.Empty<object>());
}
```

## Client-Side Usage Examples

### Example 1: Search by IoT Device ID
```javascript
var configuration = {
    filterPredicate: 'IoTDevice.Contains("device123")',
    dealerId: dealerGuid
};
```

### Example 2: Search by CCID
```javascript
var configuration = {
    filterPredicate: 'CCID.Contains("CC001")',
    filterParameters: '[{"TypeName":"System.String","Value":"CC001"}]'
};
```

### Example 3: Multiple Search Criteria
```javascript
var configuration = {
    filterPredicate: 'IoTDevice.Contains(@0) && Status == @1',
    filterParameters: '[{"TypeName":"System.String","Value":"device"},{"TypeName":"System.Int32","Value":1}]'
};
```

## Expected Behavior

### Before Fix
- Search for "device123" → Returns all available modules for dealer
- Search for "CC001" → Returns all available modules for dealer  
- Search for "NonExistent" → Returns all available modules for dealer

### After Fix
- Search for "device123" → Returns only modules with "device123" in IoTDevice field
- Search for "CC001" → Returns only modules with "CC001" in CCID field
- Search for "NonExistent" → Returns empty collection
- Invalid search parameters → Falls back to returning all available modules (graceful degradation)

## Error Handling
- **Invalid JSON**: Gracefully ignores search parameters and returns all available modules
- **Missing parameters**: Works normally without search filtering
- **Empty search**: Returns all available modules
- **Empty dealer ID**: Returns all available modules across all dealers (existing behavior preserved)

## Testing
The fix can be tested by:
1. ✅ Calling with IoTDevice search - should filter by device ID
2. ✅ Calling with CCID search - should filter by CCID
3. ✅ Calling with multiple criteria - should apply all filters
4. ✅ Calling with no matching results - should return empty collection
5. ✅ Calling with invalid JSON - should gracefully fall back
6. ✅ Calling without search parameters - should work as before (backward compatibility)

## Files Modified
- `CustomCode\BusinessLayerServerComponents\ModuleUtilities.cs` - Main implementation

## Backward Compatibility
✅ Fully backward compatible - existing calls without search parameters work exactly as before.

## Performance Impact
- Minimal performance impact when no search parameters are provided
- Search queries are optimized by combining filters in a single database call
- Existing performance logging and monitoring preserved
