﻿-- GENERAL TODOs:
-- 1. Think of a better approach than a full table scan for multi search

CREATE OR ALTER   VIEW [dbo].[VehiclesPerModelReport] AS
SELECT TOP(20) ID = NEWID(), ModelId = v.ModelId, NumberOfVehicles = COUNT(*) FROM Vehicle v INNER JOIN Model m ON v.ModelId = m.Id GROUP BY v.ModelId ORDER BY COUNT(*) DESC
GO

CREATE OR ALTER VIEW [dbo].[LoggedHoursVersusSeatHoursView] AS
SELECT
    Id = NEWID(),
	(CASE WHEN GROUPING(de.Id) = 0 THEN de.Id ELSE NULL END) AS DealerId,
    (CASE WHEN GROUPING(c.Id) = 0 THEN c.Id ELSE NULL END) AS CustomerId,
    (CASE WHEN GROUPING(s.Id) = 0 THEN s.Id ELSE NULL END) AS SiteId,
    (CASE WHEN GROUPING(d.Id) = 0 THEN d.Id ELSE NULL END) AS DepartmentId,
    DATEPART(YEAR, main.EndTime) * 368 + DATEPART(MONTH, main.EndTime) * 31 + DATEPART(DAY,  CONVERT(varchar(19), DATEADD(HOUR, tz.UTCOffset, main.EndTime), 120)) as [Order],
    DATEPART(YEAR, main.EndTime) as [Year],
    DATEPART(MONTH, main.EndTime) as [Month],
    DATEPART(DAY, CONVERT(varchar(19), DATEADD(HOUR, tz.UTCOffset, main.EndTime), 120)) as [Day],
    ROUND(SUM((CASE WHEN io.Name = '0' THEN CAST(Usage AS DECIMAL(10, 2))/3600 ELSE 0 END)), 2) AS [LoggedHours],
    ROUND(SUM((CASE WHEN io.Name = '4' OR io.Name = 'SEAT' THEN CAST(Usage AS DECIMAL(10, 2))/3600 ELSE 0 END)), 2) AS [SeatHours],
	ROUND(SUM((CASE WHEN io.Name = 'HYDR' OR io.Name = 'HYDL' OR io.Name = '1' THEN CAST(Usage AS DECIMAL(10, 2))/3600 ELSE 0 END)), 2) AS [HydraulicHours],
	ROUND(SUM((CASE WHEN io.Name = 'TRACK' OR io.Name = '2' THEN CAST(Usage AS DECIMAL(10, 2))/3600 ELSE 0 END)), 2) AS [TractionHours]
FROM
    Session AS main
    INNER JOIN SessionDetails AS sd ON main.id = sd.SessionID
    INNER JOIN IOFIELD AS io ON io.ID = sd.IOFIELDId
    LEFT JOIN dbo.Vehicle v ON v.Id = main.VehicleId
    LEFT JOIN dbo.Department d ON d.Id = v.DepartmentId
    LEFT JOIN dbo.Site s ON s.Id = d.SiteId			
    LEFT JOIN dbo.Customer c ON c.Id = s.CustomerId
	LEFT JOIN dbo.Dealer de ON de.Id = c.DealerId
	LEFT JOIN dbo.Timezone tz ON tz.id = s.TimezoneId
WHERE
    main.DriverID IS NOT NULL AND (io.Name = '0' OR io.Name = '1' OR io.Name = '2' OR io.Name = '4' OR io.Name = 'SEAT' OR io.Name = 'HYDR' OR io.Name = 'HYDL' OR io.Name = 'TRACK') AND main.EndTime >= DATEADD(MONTH, -3, GETDATE())
GROUP BY
    GROUPING SETS (
        (de.Id, c.Id, s.Id, d.Id),  -- All levels
        (de.Id, c.Id, s.Id),       -- Customer and Site
        (de.Id, c.Id, d.Id),       -- Customer and Department
        (s.Id, d.Id),       -- Site and Department
        (de.Id, c.Id),             -- Customer only
        (s.Id),             -- Site only
        (d.Id),             -- Department only
        ()                  -- Total aggregation
    ),
    DATEPART(YEAR, main.EndTime),
    DATEPART(MONTH, main.EndTime),
    DATEPART(DAY,  CONVERT(varchar(19), DATEADD(HOUR, tz.UTCOffset, main.EndTime), 120))
GO

CREATE OR ALTER     VIEW [dbo].[ImpactsForVehicleView] AS
SELECT NEWID() AS [Id], i.Id AS [ImpactId], v.Id AS [VehicleId], d.Id AS [DriverId] FROM Vehicle v INNER JOIN [Session] s ON s.VehicleId = v.Id INNER JOIN Impact i on i.SessionId = s.ID INNER JOIN Driver d ON d.Id = s.DriverId
GO

CREATE OR ALTER   VIEW [dbo].[ChecklistFailureView] as
SELECT  NEWID() AS [Id],main.PreOperationalChecklistId, (select count(*) from ChecklistDetail as d
INNER JOIN PreOperationalChecklist as p on p.ID = d.PreOperationalChecklistId
INNER JOIN ChecklistResult as r on r.ID = d.ChecklistResultID
where Answer <> p.ExpectedAnswer
And r.StartTime  >= DATEADD(MONTH, -3, GETDATE()) 
And d.PreOperationalChecklistId = main.PreOperationalChecklistId
group by d.PreOperationalChecklistId
) AS [NumberOfFailedAnswersPerQuestion],
  	(select count(*) from ChecklistDetail as d
INNER JOIN PreOperationalChecklist as p on p.ID = d.PreOperationalChecklistId
INNER JOIN ChecklistResult as r on r.ID = d.ChecklistResultID
where r.StartTime  >= DATEADD(MONTH, -3, GETDATE()) 
And d.PreOperationalChecklistId = main.PreOperationalChecklistId
group by d.PreOperationalChecklistId
) AS [NumberOfTotalAnswersPerQuestion]


FROM ChecklistDetail AS main 
INNER JOIN ChecklistResult as r on r.ID = main.ChecklistResultID
WHERE r.StartTime >= DATEADD(MONTH, -3, GETDATE()) GROUP BY main.PreOperationalChecklistId


GO

CREATE OR ALTER  VIEW [dbo].[ChecklistFailurePerVechicleView] as
SELECT  NEWID() AS [Id],main.PreOperationalChecklistId,ms.VehicleId, (select count(*) from ChecklistDetail as d
INNER JOIN PreOperationalChecklist as p on p.ID = d.PreOperationalChecklistId
INNER JOIN ChecklistResult as r on r.ID = d.ChecklistResultID
INNER JOIN Session as s on s.Id = r.SessionId1
where Answer <> p.ExpectedAnswer
And r.StartTime  >= DATEADD(MONTH, -3, GETDATE()) 
And d.PreOperationalChecklistId = main.PreOperationalChecklistId
And s.VehicleId = ms.VehicleId
group by d.PreOperationalChecklistId, s.VehicleId
) AS [NumberOfFailedAnswersPerQuestionPerVechicle],
  	(select count(*) from ChecklistDetail as d
INNER JOIN PreOperationalChecklist as p on p.ID = d.PreOperationalChecklistId
INNER JOIN ChecklistResult as r on r.ID = d.ChecklistResultID
INNER JOIN Session as s on s.Id = r.SessionId1
where r.StartTime  >= DATEADD(MONTH, -3, GETDATE()) 
And d.PreOperationalChecklistId = main.PreOperationalChecklistId
And s.VehicleId = ms.VehicleId
group by d.PreOperationalChecklistId, s.VehicleId
) AS [NumberOfTotalAnswersPerQuestionPerVechicle]




FROM ChecklistDetail AS main 
INNER JOIN ChecklistResult as r on r.ID = main.ChecklistResultID
INNER JOIN Session as ms on ms.Id = r.SessionId1
WHERE r.StartTime >= DATEADD(MONTH, -3, GETDATE()) GROUP BY main.PreOperationalChecklistId, ms.VehicleId


GO


CREATE OR ALTER VIEW [dbo].[VehicleToPreOpChecklistView] AS
SELECT
	NEWID() AS Id,
    V.Id AS VehicleId,
    POC.Id AS PreOperationalChecklistId,
	POC.Active 
FROM Vehicle V
	INNER JOIN Department D ON V.DepartmentId = D.Id
    INNER JOIN Model M ON V.ModelId = M.Id
    INNER JOIN DepartmentChecklist DC ON D.Id = DC.DepartmentId AND M.Id = DC.ModelId
    INNER JOIN PreOperationalChecklist POC ON DC.Id = POC.SiteChecklistId;
GO

CREATE OR ALTER VIEW [dbo].[CustomerToModel] AS
SELECT
    MAX(NewId) AS Id,
    CustomerId,
    ModelId
FROM (
    SELECT
        NEWID() AS NewId,
        c.Id AS CustomerId,
        m.Id AS ModelId
    FROM
        Customer c
    JOIN
        Site s ON c.Id = s.CustomerId
    JOIN
        Department d ON s.Id = d.SiteId
    JOIN
        Vehicle v ON d.Id = v.DepartmentId
    JOIN
        Model m ON v.ModelId = m.Id
) AS Subquery
GROUP BY CustomerId, ModelId;
GO

CREATE OR ALTER VIEW [dbo].[CustomerToPerson] AS
SELECT
    MAX(NewId) AS Id,
    CustomerId,
    PersonId
FROM (
    SELECT
        NEWID() AS NewId,
        c.Id AS CustomerId,
        p.Id AS PersonId
    FROM
        Customer c
    JOIN
        Site s ON c.Id = s.CustomerId
    JOIN
        Person p ON p.SiteId = s.Id
) AS Subquery
GROUP BY CustomerId, PersonId;
GO

-- Create the dashboard card view by joining the necessary tables directly
-- Create the final view by joining the necessary tables directly
CREATE OR ALTER VIEW [dbo].[DashboardVehicleCardView] AS
WITH SessionDateBoundaries AS (
    SELECT 
        DATEADD(HOUR, -24, GETDATE()) AS LastTwentyFourHours,
        DATEADD(HOUR, -72, GETDATE()) AS LastSeventyTwoHours,
        DATEADD(DAY, -1, GETDATE()) AS LastDay,
        DATEADD(DAY, -2, GETDATE()) AS TwoDaysAgo,
        DATEADD(DAY, -7, GETDATE()) AS LastWeek,
        DATEADD(DAY, -28, GETDATE()) AS LastFourWeeks
),
VehicleBaseData AS (
    SELECT 
        v.Id AS VehicleId,
        v.LastSessionDate,
        v.DepartmentId,
        d.SiteId,
        s.CustomerId,
        c.DealerId,
        ISNULL(vos.VORStatus, 0) AS VORStatus,
        CASE WHEN v.LastSessionDate < db.LastTwentyFourHours THEN 1 ELSE 0 END AS IsInactiveTwentyFourHours,
        CASE WHEN v.LastSessionDate < db.LastSeventyTwoHours THEN 1 ELSE 0 END AS IsInactiveSeventyTwoHours
    FROM 
        dbo.Vehicle v
        CROSS JOIN SessionDateBoundaries db
        INNER JOIN dbo.Department d ON v.DepartmentId = d.Id
        INNER JOIN dbo.Site s ON d.SiteId = s.Id
        INNER JOIN dbo.Customer c ON s.CustomerId = c.Id
        LEFT JOIN dbo.VehicleOtherSettings vos ON v.VehicleOtherSettingsId = vos.Id
),
ImpactStats AS (
    SELECT 
        v.VehicleId,
        SUM(CASE 
            WHEN imp.ImpactDateTime >= db.LastDay 
                AND imp.Threshold != 0 
                AND imp.ShockValue > (10 * imp.Threshold) 
            THEN 1 ELSE 0 END) AS RedImpactCountToday,
        SUM(CASE 
            WHEN imp.ImpactDateTime >= db.TwoDaysAgo 
                AND imp.ImpactDateTime < db.LastDay
                AND imp.Threshold != 0 
                AND imp.ShockValue > (10 * imp.Threshold) 
            THEN 1 ELSE 0 END) AS RedImpactCountYesterday,
        CAST(SUM(CASE 
            WHEN imp.ImpactDateTime >= db.LastWeek
                AND imp.Threshold != 0 
                AND imp.ShockValue > (10 * imp.Threshold) 
            THEN 1 ELSE 0 END) AS FLOAT) / 7.0 AS RedImpactDailyAverageCountLastWeek,
        CAST(SUM(CASE 
            WHEN imp.ImpactDateTime >= db.LastFourWeeks
                AND imp.Threshold != 0 
                AND imp.ShockValue > (10 * imp.Threshold) 
            THEN 1 ELSE 0 END) AS FLOAT) / 28.0 AS RedImpactDailyAverageCountLastFourWeeks
    FROM 
        VehicleBaseData v
        CROSS JOIN SessionDateBoundaries db
        LEFT JOIN dbo.Session sess ON v.VehicleId = sess.VehicleId
        LEFT JOIN dbo.Impact imp ON sess.Id = imp.SessionId
    GROUP BY 
        v.VehicleId
),
OverlappingSessions AS (
    SELECT 
        sess1.VehicleId,
        COUNT(*) AS OverlappingSessionCount
    FROM 
        dbo.Session sess1
        INNER JOIN dbo.Session sess2 ON sess1.VehicleId = sess2.VehicleId
        CROSS JOIN SessionDateBoundaries db
    WHERE 
        sess1.StartTime < sess2.StartTime
        AND sess1.EndTime > sess2.StartTime
        AND sess1.StartTime >= db.LastTwentyFourHours
    GROUP BY 
        sess1.VehicleId
),
AggregatedData AS (
    SELECT 
        vbd.DealerId,
        vbd.CustomerId,
        vbd.SiteId,
        vbd.DepartmentId,
        COUNT(DISTINCT vbd.VehicleId) AS VehiclesCount,
        SUM(vbd.IsInactiveTwentyFourHours) AS InactiveVehicleCountLastTwentyFourHours,
        SUM(vbd.IsInactiveSeventyTwoHours) AS InactiveVehicleCountLastSeventyTwoHours,
        SUM(CASE WHEN vbd.VORStatus = 1 THEN 1 ELSE 0 END) AS VehicleVORModeCount,
        SUM(ISNULL(imp.RedImpactCountToday, 0)) AS RedImpactCountToday,
        SUM(ISNULL(imp.RedImpactCountYesterday, 0)) AS RedImpactCountYesterday,
        AVG(ISNULL(imp.RedImpactDailyAverageCountLastWeek, 0)) AS RedImpactDailyAverageCountLastWeek,
        AVG(ISNULL(imp.RedImpactDailyAverageCountLastFourWeeks, 0)) AS RedImpactDailyAverageCountLastFourWeeks
    FROM 
        VehicleBaseData vbd
        LEFT JOIN ImpactStats imp ON vbd.VehicleId = imp.VehicleId
    GROUP BY 
        GROUPING SETS (
            (vbd.DealerId, vbd.CustomerId, vbd.SiteId, vbd.DepartmentId),
            (vbd.DealerId, vbd.CustomerId, vbd.SiteId),
            (vbd.DealerId, vbd.CustomerId, vbd.DepartmentId),
            (vbd.SiteId, vbd.DepartmentId),
            (vbd.DealerId, vbd.CustomerId),
            (vbd.SiteId),
            (vbd.DepartmentId),
            ()
        )
)

SELECT 
    NEWID() AS Id,
    ad.DealerId,
    ad.CustomerId,
    ad.SiteId,
    ad.DepartmentId,
    ad.VehiclesCount,
    ISNULL(SUM(os.OverlappingSessionCount), 0) AS SessionCountLastTwentyFourHours,
    ad.InactiveVehicleCountLastTwentyFourHours,
    ad.InactiveVehicleCountLastSeventyTwoHours,
    ad.VehicleVORModeCount,
    ad.RedImpactCountToday,
    ad.RedImpactCountYesterday,
    ad.RedImpactDailyAverageCountLastWeek,
    ad.RedImpactDailyAverageCountLastFourWeeks
FROM 
    AggregatedData ad
    LEFT JOIN OverlappingSessions os ON 
        ad.DealerId IS NULL AND 
        ad.CustomerId IS NULL AND 
        ad.SiteId IS NULL AND 
        ad.DepartmentId IS NULL
GROUP BY
    ad.DealerId,
    ad.CustomerId,
    ad.SiteId,
    ad.DepartmentId,
    ad.VehiclesCount,
    ad.InactiveVehicleCountLastTwentyFourHours,
    ad.InactiveVehicleCountLastSeventyTwoHours,
    ad.VehicleVORModeCount,
    ad.RedImpactCountToday,
    ad.RedImpactCountYesterday,
    ad.RedImpactDailyAverageCountLastWeek,
    ad.RedImpactDailyAverageCountLastFourWeeks;
GO

CREATE OR ALTER VIEW [dbo].[DashboardDriverCardView] AS
WITH PersonData AS (
    SELECT 
        NEWID() AS [Id], 
        (CASE WHEN GROUPING(c.Id) = 0 THEN c.Id ELSE NULL END) AS CustomerId,
        (CASE WHEN GROUPING(s.Id) = 0 THEN s.Id ELSE NULL END) AS SiteId,
        (CASE WHEN GROUPING(d.Id) = 0 THEN d.Id ELSE NULL END) AS DepartmentId,
        (CASE WHEN GROUPING(de.Id) = 0 THEN de.Id ELSE NULL END) AS DealerId,
        COUNT(DISTINCT dr.Id) AS DriversCount,
        COUNT(CASE WHEN ld.ExpiryDate < GETDATE() AND p.DriverId IS NOT NULL THEN 1 ELSE NULL END) AS ExpiredLicensesCount,
        COUNT(CASE WHEN dr.LicenceDetailId IS NULL AND p.DriverId IS NOT NULL THEN 1 ELSE NULL END) AS NoLicenseDriversCount,
        COUNT(CASE WHEN dr.LastSessionDate < DATEADD(DAY, -7, GETDATE()) AND p.DriverId IS NOT NULL THEN 1 ELSE NULL END) AS NonActiveDriversCountLastWeek
    FROM 
        dbo.Person p
        LEFT JOIN dbo.Customer c ON p.CustomerId = c.Id
        LEFT JOIN dbo.Dealer de ON c.DealerId = de.Id
        LEFT JOIN dbo.Driver dr ON p.DriverId = dr.Id
        LEFT JOIN dbo.Department d ON p.DepartmentId = d.Id
        LEFT JOIN dbo.Site s ON p.SiteId = s.Id
        LEFT JOIN dbo.LicenceDetail ld ON dr.LicenceDetailId = ld.Id
    GROUP BY 
        GROUPING SETS (
            (de.Id, c.Id, s.Id, d.Id),  -- All levels
            (de.Id, c.Id, s.Id),       -- Customer and Site
            (de.Id, c.Id, d.Id),       -- Customer and Department
            (s.Id, d.Id),              -- Site and Department
            (de.Id, c.Id),             -- Customer only
            (s.Id),                    -- Site only
            (d.Id),                    -- Department only
            ()                         -- Total aggregation
        )
)

SELECT 
    NEWID() as Id,
    DealerId,
    CustomerId,
    SiteId,
    DepartmentId,
    SUM(DriversCount) AS DriversCount,
    SUM(ExpiredLicensesCount) AS ExpiredLicensesCount,
    SUM(NoLicenseDriversCount) AS NoLicenseDriversCount,
    SUM(NonActiveDriversCountLastWeek) AS NonActiveDriversCountLastWeek
FROM PersonData
GROUP BY
    DealerId,
    CustomerId,
    SiteId,
    DepartmentId
GO

-- Vehicle Utilization Last 12h View for Dashboard
CREATE OR ALTER VIEW [dbo].[VehicleUtilizationLastTwelveHoursView] AS
WITH HourSeries AS (
    SELECT DATEADD(HOUR, -11, GETDATE()) AS HourStart
    UNION ALL
    SELECT DATEADD(HOUR, 1, HourStart)
    FROM HourSeries
    WHERE HourStart < GETDATE()
), 
HourSeriesWithRowNumber AS (
    SELECT *,
           ROW_NUMBER() OVER (PARTITION BY DealerId, CustomerId, SiteId, DepartmentId, TimePeriod ORDER BY Id) AS RowNum
    FROM (
        SELECT
            Id = NEWID(),
			(CASE WHEN GROUPING(de.Id) = 0 THEN de.Id ELSE NULL END) AS DealerId,
            (CASE WHEN GROUPING(c.Id) = 0 THEN c.Id ELSE NULL END) AS CustomerId,
            (CASE WHEN GROUPING(s.Id) = 0 THEN s.Id ELSE NULL END) AS SiteId,
            (CASE WHEN GROUPING(d.Id) = 0 THEN d.Id ELSE NULL END) AS DepartmentId,
            CONVERT(NVARCHAR(5), DATEADD(MINUTE, -DATEPART(MINUTE, HourStart), HourStart), 108) AS TimePeriod,
            COALESCE(COUNT(sess.StartTime), 0) AS NumberOfSessions
        FROM
			HourSeries hs
			CROSS JOIN dbo.Vehicle v
			LEFT JOIN dbo.Department d ON v.DepartmentId = d.Id
			LEFT JOIN dbo.Site s ON d.SiteId = s.Id
			LEFT JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
			LEFT JOIN dbo.Customer c ON s.CustomerId = c.Id
			LEFT JOIN dbo.Dealer de ON c.DealerId = de.Id
			LEFT JOIN dbo.Session sess ON v.Id = sess.VehicleId 
				AND DATEPART(HOUR, DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), sess.StartTime)) = DATEPART(HOUR, HourStart)
				AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), sess.StartTime) >= DATEADD(HOUR, -12, GETDATE())

        GROUP BY
            GROUPING SETS (
                (de.Id, c.Id, s.Id, d.Id),  -- All levels
                (de.Id, c.Id, s.Id),       -- Customer and Site
                (de.Id, c.Id, d.Id),       -- Customer and Department
                (s.Id, d.Id),       -- Site and Department
                (de.Id, c.Id),             -- Customer only
                (s.Id),             -- Site only
                (d.Id),             -- Department only
                ()                  -- Total aggregation
            ),
            HourStart
    ) AS Subquery
)
SELECT 
    Id,
	DealerId,
    CustomerId, 
    SiteId, 
    DepartmentId, 
    TimePeriod, 
    NumberOfSessions
FROM HourSeriesWithRowNumber
WHERE RowNum = 1
GO


-- Driver License Expiry View for Dashboard
CREATE OR ALTER VIEW [dbo].[DriverLicenseExpiryView] AS
SELECT
    NEWID() AS Id,
	de.Id AS DealerId,
    c.Id AS CustomerId,
    p.SiteId,
    p.DepartmentId,
    '< 3 months' AS TimePeriod,
    SUM(CASE WHEN ld.ExpiryDate > DATEADD(MONTH, 1, GETDATE()) AND ld.ExpiryDate <= DATEADD(MONTH, 3, GETDATE()) THEN 1 ELSE 0 END) AS ExpiredLicenseCount
FROM dbo.Person p
LEFT JOIN dbo.Customer c ON p.CustomerId = c.Id
LEFT JOIN dbo.Dealer de ON c.DealerId = de.Id
LEFT JOIN dbo.Driver dr ON p.DriverId = dr.Id
LEFT JOIN dbo.Department d ON p.DepartmentId = d.Id
LEFT JOIN dbo.Site s ON p.SiteId = s.Id
LEFT JOIN dbo.LicenceDetail ld ON dr.LicenceDetailId = ld.Id
GROUP BY 
    GROUPING SETS (
        (de.Id, c.Id, p.SiteId, p.DepartmentId),  -- All levels
        (de.Id, c.Id, p.SiteId),       -- Customer and Site
        (de.Id, c.Id, p.DepartmentId), -- Customer and Department
        (p.SiteId, p.DepartmentId), -- Site and Department
        (de.Id, c.Id),             -- Customer only
        (p.SiteId),         -- Site only
        (p.DepartmentId),   -- Department only
        ()                  -- Total aggregation
    )

UNION ALL

SELECT
    NEWID() AS Id,
	de.Id AS DealerId,
    c.Id AS CustomerId,
    p.SiteId,
    p.DepartmentId,
    '< 1 month' AS TimePeriod,
    SUM(CASE WHEN ld.ExpiryDate >= GETDATE() AND ld.ExpiryDate < DATEADD(MONTH, 1, GETDATE()) THEN 1 ELSE 0 END) AS ExpiredLicenseCount
FROM dbo.Person p
LEFT JOIN dbo.Customer c ON p.CustomerId = c.Id
LEFT JOIN dbo.Dealer de ON c.DealerId = de.Id
LEFT JOIN dbo.Driver dr ON p.DriverId = dr.Id
LEFT JOIN dbo.Department d ON p.DepartmentId = d.Id
LEFT JOIN dbo.Site s ON p.SiteId = s.Id
LEFT JOIN dbo.LicenceDetail ld ON dr.LicenceDetailId = ld.Id
GROUP BY 
    GROUPING SETS (
        (de.Id, c.Id, p.SiteId, p.DepartmentId),  -- All levels
        (de.Id, c.Id, p.SiteId),       -- Customer and Site
        (de.Id, c.Id, p.DepartmentId), -- Customer and Department
        (p.SiteId, p.DepartmentId), -- Site and Department
        (de.Id, c.Id),             -- Customer only
        (p.SiteId),         -- Site only
        (p.DepartmentId),   -- Department only
        ()                  -- Total aggregation
    )

UNION ALL

SELECT
    NEWID() AS Id,
	de.Id AS DealerId,
    c.Id AS CustomerId,
    p.SiteId,
    p.DepartmentId,
    'Overdue' AS TimePeriod,
    SUM(CASE WHEN ld.ExpiryDate < GETDATE() THEN 1 ELSE 0 END) AS ExpiredLicenseCount
FROM dbo.Person p
LEFT JOIN dbo.Customer c ON p.CustomerId = c.Id
LEFT JOIN dbo.Dealer de ON c.DealerId = de.Id
LEFT JOIN dbo.Driver dr ON p.DriverId = dr.Id
LEFT JOIN dbo.Department d ON p.DepartmentId = d.Id
LEFT JOIN dbo.Site s ON p.SiteId = s.Id
LEFT JOIN dbo.LicenceDetail ld ON dr.LicenceDetailId = ld.Id
GROUP BY 
    GROUPING SETS (
        (de.Id, c.Id, p.SiteId, p.DepartmentId),  -- All levels
        (de.Id, c.Id, p.SiteId),       -- Customer and Site
        (de.Id, c.Id, p.DepartmentId), -- Customer and Department
        (p.SiteId, p.DepartmentId), -- Site and Department
        (de.Id, c.Id),             -- Customer only
        (p.SiteId),         -- Site only
        (p.DepartmentId),   -- Department only
        ()                  -- Total aggregation
    )
GO


-- Today's Impacts View
CREATE OR ALTER VIEW [dbo].[TodaysImpactView] AS
-- View query for Amber impact level
SELECT 
    NEWID() AS [Id],
    (CASE WHEN GROUPING(de.Id) = 0 THEN de.Id ELSE NULL END) AS DealerId,
    (CASE WHEN GROUPING(c.Id) = 0 THEN c.Id ELSE NULL END) AS CustomerId,
    (CASE WHEN GROUPING(s.Id) = 0 THEN s.Id ELSE NULL END) AS SiteId,
    (CASE WHEN GROUPING(d.Id) = 0 THEN d.Id ELSE NULL END) AS DepartmentId,
    0 AS ImpactType,
    'Amber' AS ImpactLevel,
    ISNULL(SUM(CASE 
        WHEN imp.ShockValue >= imp.Threshold * 5 AND imp.ShockValue < imp.Threshold * 10 THEN 1 
        ELSE 0 
    END), 0) AS NumberOfImpacts
FROM 
    (SELECT 1 AS dummy) AS dummy
    CROSS JOIN dbo.Vehicle v
    LEFT JOIN dbo.Department d ON d.Id = v.DepartmentId
    LEFT JOIN dbo.Site s ON s.Id = d.SiteId
    LEFT JOIN dbo.Customer c ON c.Id = s.CustomerId
    LEFT JOIN dbo.Dealer de ON c.DealerId = de.Id
    LEFT JOIN dbo.Session sess ON sess.VehicleId = v.Id
	LEFT JOIN dbo.Timezone tz ON tz.id = s.TimezoneId
    LEFT JOIN dbo.Impact imp ON imp.SessionId = sess.Id 
    AND imp.Threshold != 0 
    AND CAST(imp.ImpactDateTime AS DATE) = CAST(DATEADD(HOUR, tz.UTCOffset, GETDATE()) AS DATE)
GROUP BY 
    GROUPING SETS (
        (de.Id, c.Id, s.Id, d.Id),
        (de.Id, c.Id, s.Id),
        (de.Id, c.Id, d.Id),
        (s.Id, d.Id),
        (de.Id, c.Id),
        (s.Id),
        (d.Id),
        ()
    )

UNION ALL

-- View query for Red impact level
SELECT 
    NEWID() AS [Id],
    (CASE WHEN GROUPING(de.Id) = 0 THEN de.Id ELSE NULL END) AS DealerId,
    (CASE WHEN GROUPING(c.Id) = 0 THEN c.Id ELSE NULL END) AS CustomerId,
    (CASE WHEN GROUPING(s.Id) = 0 THEN s.Id ELSE NULL END) AS SiteId,
    (CASE WHEN GROUPING(d.Id) = 0 THEN d.Id ELSE NULL END) AS DepartmentId,
    1 AS ImpactType,
    'Red' AS ImpactLevel,
    ISNULL(SUM(CASE 
        WHEN imp.ShockValue > imp.Threshold * 10 THEN 1 
        ELSE 0 
    END), 0) AS NumberOfImpacts
FROM 
    (SELECT 1 AS dummy) AS dummy
    CROSS JOIN dbo.Vehicle v
    LEFT JOIN dbo.Department d ON d.Id = v.DepartmentId
    LEFT JOIN dbo.Site s ON s.Id = d.SiteId
    LEFT JOIN dbo.Customer c ON c.Id = s.CustomerId
    LEFT JOIN dbo.Dealer de ON c.DealerId = de.Id
    LEFT JOIN dbo.Session sess ON sess.VehicleId = v.Id
	LEFT JOIN dbo.Timezone tz ON tz.id = s.TimezoneId
    LEFT JOIN dbo.Impact imp ON imp.SessionId = sess.Id AND imp.Threshold <> 0 AND CAST(imp.ImpactDateTime AS DATE) = CAST(DATEADD(HOUR, tz.UTCOffset, GETDATE()) AS DATE)
GROUP BY 
    GROUPING SETS (
        (de.Id, c.Id, s.Id, d.Id),
        (de.Id, c.Id, s.Id),
        (de.Id, c.Id, d.Id),
        (s.Id, d.Id),
        (de.Id, c.Id),
        (s.Id),
        (d.Id),
        ()
    );

GO
-- End

-- Today's Preop Check View
CREATE OR ALTER VIEW [dbo].[TodaysPreopCheckView] AS
WITH Counts AS (
SELECT
        NEWID() AS Id,
			CASE WHEN GROUPING(de.Id) = 0 THEN de.Id ELSE NULL END AS DealerId,
		    CASE WHEN GROUPING(c.Id) = 1 THEN NULL ELSE c.Id END AS CustomerId,
            CASE WHEN GROUPING(s.Id) = 1 THEN NULL ELSE s.Id END AS SiteId,
            CASE WHEN GROUPING(d.Id) = 1 THEN NULL ELSE d.Id END AS DepartmentId,
        COUNT(CASE WHEN CAST(DATEADD(HOUR, tz.UTCOffset, ckl.StartTime) AS DATE) = CAST(DATEADD(HOUR, tz.UTCOffset, GETDATE()) AS DATE) AND ckl.EndTime IS NOT NULL THEN 1 ELSE NULL END) AS Status0Count,
        COUNT(CASE WHEN CAST(DATEADD(HOUR, tz.UTCOffset, ckl.StartTime) AS DATE) = CAST(DATEADD(HOUR, tz.UTCOffset, GETDATE()) AS DATE) AND ckl.EndTime IS NULL THEN 1 ELSE NULL END) AS Status1Count
    FROM 
    dbo.Vehicle v
	LEFT JOIN dbo.Department d ON d.Id = v.DepartmentId
	LEFT JOIN dbo.Site s ON s.Id =d.SiteId			
    LEFT JOIN dbo.Customer c ON c.Id = s.CustomerId
	LEFT JOIN dbo.Dealer de ON de.id = c.DealerId
    LEFT JOIN dbo.Session sess ON  sess.VehicleId = v.Id
	LEFT JOIN dbo.ChecklistResult ckl ON sess.Id = ckl.SessionId1
	LEFT JOIN dbo.TimeZone tz on s.TimezoneId = tz.id
	GROUP BY 
    GROUPING SETS (
        (de.id, c.Id, s.Id, d.Id),  -- All levels
        (de.id, c.Id, s.Id),       -- Customer and Site
        (de.id, c.Id, d.Id),       -- Customer and Department
        (s.Id, d.Id),       -- Site and Department
        (de.id, c.Id),             -- Customer only
        (s.Id),             -- Site only
        (d.Id),             -- Department only
        ()                  -- Total aggregation
		)
)
SELECT
    Id,
	DealerId,
	CustomerId,
    SiteId,
    DepartmentId,
    0 AS Status,
    ROUND(
        CASE
            WHEN COALESCE(Status0Count + Status1Count, 0) = 0 THEN 0
            ELSE CAST(Status0Count AS DECIMAL(18, 4)) / COALESCE(Status0Count + Status1Count, 1) * 100
        END,
        2
    ) AS Percentage
FROM Counts

UNION ALL

SELECT
    Id,
	DealerId,
	CustomerId,
    SiteId,
    DepartmentId,
    1 AS Status,
    ROUND(
        CASE
            WHEN COALESCE(Status0Count + Status1Count, 0) = 0 THEN 0
            ELSE CAST(Status1Count AS DECIMAL(18, 4)) / COALESCE(Status0Count + Status1Count, 1) * 100
        END,
        2
    ) AS Percentage
FROM Counts

UNION ALL

SELECT
    NEWID() AS Id,
	CASE WHEN GROUPING(de.Id) = 0 THEN de.Id ELSE NULL END AS DealerId,
    CASE WHEN GROUPING(c.Id) = 1 THEN NULL ELSE c.Id END AS CustomerId,
    CASE WHEN GROUPING(s.Id) = 1 THEN NULL ELSE s.Id END AS SiteId,
    CASE WHEN GROUPING(d.Id) = 1 THEN NULL ELSE d.Id END AS DepartmentId,
    2 AS Status,
    CASE WHEN 
	  EXISTS (
       SELECT 1
        FROM [dbo].[ChecklistDetail] cd
        INNER JOIN [dbo].[PreOperationalChecklist] poc ON cd.[PreOperationalChecklistId] = poc.[Id] and poc.[Critical] = 1 and cd.[Answer] <> poc.[ExpectedAnswer]
		LEFT JOIN dbo.ChecklistResult ckl ON cd.[ChecklistResultId] = ckl.[Id]
		WHERE CAST(ckl.StartTime AS DATE) = CAST(GETDATE() AS DATE)
     ) THEN 
	     ROUND(
            CASE
               WHEN COALESCE(COUNT(*), 0) = 0 THEN 0
               ELSE CAST(COUNT(*) AS DECIMAL(18, 4)) / COALESCE(COUNT(*), 1) * 100
            END,
            2
       ) ElSE 0
	END AS Percentage
FROM      
    dbo.Vehicle v
	LEFT JOIN dbo.Department d ON d.Id = v.DepartmentId
	LEFT JOIN dbo.Site s ON s.Id = d.SiteId			
    LEFT JOIN dbo.Customer c ON c.Id = s.CustomerId
	LEFT JOIN dbo.Dealer de ON de.id = c.DealerId
    LEFT JOIN dbo.Session sess ON  sess.VehicleId = v.Id
	LEFT JOIN dbo.ChecklistResult ckl ON sess.Id = ckl.SessionId1
	GROUP BY 
    GROUPING SETS (
        (de.id, c.Id, s.Id, d.Id),  -- All levels
        (de.id, c.Id, s.Id),       -- Customer and Site
        (de.id, c.Id, d.Id),       -- Customer and Department
        (s.Id, d.Id),       -- Site and Department
        (de.id, c.Id),             -- Customer only
        (s.Id),             -- Site only
        (d.Id),             -- Department only
        ()                  -- Total aggregation
	);
GO

CREATE OR ALTER   VIEW [dbo].[PersonToSiteVehicleNormalAccessView] AS

SELECT
	CASE 
		WHEN sa.PermissionId is null THEN 0
		ELSE 1
	END AS HasAccess,
    p.Id AS PersonId,
    s.Id AS SiteId,
    s.Name AS SiteName,
	ca.Id as PermissionId
FROM
    Card c
INNER JOIN Driver d ON c.Id = d.CardDetailsId
INNER JOIN Person p ON p.DriverId = d.Id
INNER JOIN Customer cust ON cust.Id = p.CustomerId
INNER JOIN Site s ON s.CustomerId = cust.Id
CROSS JOIN Permission ca
LEFT JOIN SiteVehicleNormalCardAccess sa ON sa.SiteId = s.Id AND sa.CardId = c.Id AND sa.PermissionId = ca.Id

GO




CREATE OR ALTER VIEW [dbo].[PersonToDepartmentVehicleNormalAccessView] AS

SELECT
	CASE 
		WHEN da.PermissionId is null THEN 0
		ELSE 1
	END AS HasAccess,
    p.Id AS PersonId, 
	dpt.Id AS DepartmentId, 
	CONCAT(s.Name, ' : ', dpt.Name) as DepartmentName,
	perm.Id AS PermissionId
FROM
    Card c
INNER JOIN Driver d ON c.Id = d.CardDetailsId
INNER JOIN Person p ON p.DriverId = d.Id
INNER JOIN Customer cust ON cust.Id = p.CustomerId
INNER JOIN Site s ON s.CustomerId = cust.Id
INNER JOIN Department dpt ON s.Id = dpt.SiteId
CROSS JOIN Permission perm 
INNER JOIN SiteVehicleNormalCardAccess sa ON sa.SiteId = s.Id AND sa.CardId = c.Id AND perm.Id = sa.PermissionId
LEFT JOIN DepartmentVehicleNormalCardAccess da ON da.CardId = c.Id AND da.DepartmentId = dpt.Id AND da.PermissionId = perm.Id

GO



CREATE OR ALTER VIEW [dbo].[PersonToPerVehicleNormalAccessView] AS

SELECT
	CASE 
		WHEN va.PermissionId is null THEN 0
		ELSE 1
	END AS HasAccess,
    p.Id AS PersonId, 
	v.Id AS VehicleId, 
	v.HireNo as HireNo,
	perm.Id AS PermissionId
FROM
    Card c
INNER JOIN Driver d ON c.Id = d.CardDetailsId
INNER JOIN Person p ON p.DriverId = d.Id
INNER JOIN Customer cust ON cust.Id = p.CustomerId
INNER JOIN Site s ON s.CustomerId = cust.Id
INNER JOIN Department dpt ON s.Id = dpt.SiteId
INNER JOIN Vehicle v on v.DepartmentId = dpt.Id
CROSS JOIN Permission perm 
INNER JOIN DepartmentVehicleNormalCardAccess da ON da.CardId = c.Id AND da.DepartmentId = dpt.Id AND da.PermissionId = perm.Id
INNER JOIN ModelVehicleNormalCardAccess ma on ma.CardId = c.Id AND ma.ModelId = v.ModelId AND ma.DepartmentId = dpt.Id AND ma.PermissionId = perm.Id
LEFT JOIN PerVehicleNormalCardAccess va on va.CardId = c.Id AND va.VehicleId = v.Id AND va.PermissionId = perm.Id

GO


CREATE OR ALTER VIEW [dbo].[PersonToModelVehicleNormalAccessView] AS

SELECT DISTINCT
	CASE 
		WHEN ma.PermissionId is null THEN 0
		ELSE 1
	END AS HasAccess,
    p.Id AS PersonId,  
	m.Id AS ModelId, 
	dpt.Id AS DepartmentId,
	CONCAT(dpt.Name, ' : ', m.Name) as ModelName,
	perm.Id AS PermissionId
FROM
    Card c
INNER JOIN Driver d ON c.Id = d.CardDetailsId
INNER JOIN Person p ON p.DriverId = d.Id
INNER JOIN Customer cust ON cust.Id = p.CustomerId
INNER JOIN Site s ON s.CustomerId = cust.Id
INNER JOIN Department dpt ON s.Id = dpt.SiteId
INNER JOIN Vehicle v on v.DepartmentId = dpt.Id
INNER JOIN Model m on v.ModelId = m.Id
CROSS JOIN Permission perm 
INNER JOIN DepartmentVehicleNormalCardAccess da ON da.CardId = c.Id AND da.DepartmentId = dpt.Id AND da.PermissionId = perm.Id
LEFT JOIN ModelVehicleNormalCardAccess ma on ma.CardId = c.Id AND ma.ModelId = v.ModelId AND ma.DepartmentId = dpt.Id AND ma.PermissionId = perm.Id

GO

CREATE OR ALTER VIEW [dbo].[CurrentStatusDriverView] AS
SELECT
    NEWID() AS Id,
	de.Id AS DealerId,
	'B337F798-B08E-4159-9B45-5D25195F7FCA' AS CurrentStatusCombinedViewId,
    dr.Id AS DriverId,
    dr.LastSessionDateTzAdjusted AS [LastReportedTime],
    CASE 
        WHEN sessionVehicle.ModuleIsConnected = 1 AND Session.EndTime IS NULL AND sessionVehicle.LastSessionId = dr.LastSessionId THEN 'Online'
        ELSE 'Offline'
    END AS Status,
    ISNULL(sessionVehicle.HireNo, 'N/A') AS HireNo,
    ISNULL(model.Name, 'N/A') AS Model,
	s.Name AS VehicleSite,
	d.Name AS VehicleDepartment
FROM
    dbo.Driver as dr
INNER JOIN
    dbo.Session ON Session.Id = CONVERT(uniqueidentifier, dr.LastSessionId)  -- Assuming LastSessionId is a string
INNER JOIN
    dbo.Vehicle AS sessionVehicle ON Session.VehicleId = sessionVehicle.Id
INNER JOIN
    dbo.Model AS model ON sessionVehicle.ModelId = model.Id
INNER JOIN
	dbo.Person as p ON p.DriverId = dr.Id
INNER JOIN
	dbo.Site AS s ON p.SiteId = s.Id
INNER JOIN
	dbo.Department d ON p.DepartmentId = d.id
INNER JOIN
	dbo.Customer as c ON p.CustomerId = c.Id
INNER JOIN
	dbo.Dealer as de ON de.Id = c.DealerId
WHERE
     dr.LastSessionId IS NOT NULL AND dr.LastSessionId != '';
GO

CREATE OR ALTER VIEW [dbo].[CurrentDriverStatusChartView] AS
WITH DriverCounts AS (
    SELECT
        CASE
            WHEN sess.EndTime IS NULL AND d.LastSessionId = v.LastSessionId AND v.ModuleIsConnected = 1 THEN 'Online'
            WHEN DATEDIFF(DAY, d.LastSessionDate, GETDATE()) > 7 THEN '> 7 days'
            WHEN DATEDIFF(DAY, d.LastSessionDate, GETDATE()) > 3 THEN '> 3 days'
            ELSE NULL
        END AS LastOnlineStatus,
        COUNT(*) AS NumberOfDrivers,
        c.DealerId,
		p.CustomerId,
        p.SiteId,
        p.DepartmentId
    FROM dbo.Driver d
    LEFT JOIN dbo.Session sess ON TRY_CONVERT(uniqueidentifier, d.LastSessionId) = sess.Id
    LEFT JOIN dbo.Vehicle v ON sess.VehicleId = v.Id
    LEFT JOIN dbo.Person p ON p.DriverId = d.Id
	LEFT JOIN dbo.Customer	c ON p.CustomerId = c.Id
    WHERE d.LastSessionDate IS NOT NULL
      AND d.LastSessionId IS NOT NULL
      AND (DATEDIFF(DAY, d.LastSessionDate, GETDATE()) > 3 OR CAST(d.LastSessionDate AS DATE) = CAST(GETDATE() AS DATE))
    GROUP BY 
        CASE
            WHEN sess.EndTime IS NULL AND d.LastSessionId = v.LastSessionId AND v.ModuleIsConnected = 1 THEN 'Online'
            WHEN DATEDIFF(DAY, d.LastSessionDate, GETDATE()) > 7 THEN '> 7 days'
            WHEN DATEDIFF(DAY, d.LastSessionDate, GETDATE()) > 3 THEN '> 3 days'
            ELSE NULL
        END,
        c.DealerId, p.CustomerId, p.SiteId, p.DepartmentId
)
SELECT
    Id = NEWID(),
    LastOnlineStatus,
	DealerId,
    CustomerId,
    SiteId,
    DepartmentId,
    COALESCE(SUM(NumberOfDrivers), 0) AS NumberOfDrivers
FROM DriverCounts
GROUP BY GROUPING SETS (
    (LastOnlineStatus, DealerId, CustomerId, SiteId, DepartmentId),  -- All levels
    (LastOnlineStatus, DealerId, CustomerId, SiteId),       -- Customer and Site
    (LastOnlineStatus, DealerId, CustomerId, DepartmentId),       -- Customer and Department
    (LastOnlineStatus, SiteId, DepartmentId),       -- Site and Department
    (LastOnlineStatus, DealerId, CustomerId),             -- Customer only
    (LastOnlineStatus, SiteId),             -- Site only
    (LastOnlineStatus, DepartmentId),             -- Department only
    (LastOnlineStatus)                  -- LastOnlineStatus only
);
GO

CREATE OR ALTER VIEW [dbo].[CurrentStatusVehicleView] AS
SELECT
    NEWID() AS Id,
    Vehicle.Id AS VehicleId,
	de.Id AS DealerId,
    Vehicle.LastSessionDateTzAdjusted AS LastReportedTime,
	'B337F798-B08E-4159-9B45-5D25195F7FCA' AS CurrentStatusCombinedViewId,
    CASE 
        WHEN Vehicle.ModuleIsConnected = 1 THEN 'Online'
        ELSE 'Offline'
    END AS Status,
    ISNULL(Vehicle.HireNo, 'N/A') AS [Hire No],
    ISNULL(model.Name, 'N/A') AS Model
FROM
    dbo.Vehicle
LEFT JOIN
    dbo.Model AS model ON Vehicle.ModelId = model.Id
LEFT JOIN
	dbo.Customer AS c ON Vehicle.CustomerId = c.Id
LEFT JOIN
	dbo.Dealer AS de ON de.Id = c.DealerId
-- WHERE
--     Vehicle.LastSessionId IS NOT NULL AND Vehicle.LastSessionId != '';
GO

CREATE OR ALTER VIEW [dbo].[CurrentVehicleStatusChartView] AS
WITH VehicleCounts AS (
    SELECT
        CASE
            WHEN v.ModuleIsConnected = 1 THEN 'Online'
            WHEN DATEDIFF(DAY, v.LastSessionDate, GETDATE()) > 7 THEN '> 7 days'
            WHEN DATEDIFF(DAY, v.LastSessionDate, GETDATE()) > 3 THEN '> 3 days'
            ELSE NULL
        END AS LastOnlineStatus,
        COUNT(*) AS NumberOfVehicles,
		c.DealerId,
        v.CustomerId,
        v.SiteId,
        v.DepartmentId
    FROM dbo.Vehicle v
    LEFT JOIN dbo.Session sess ON TRY_CONVERT(uniqueidentifier, v.LastSessionId) = sess.Id
	LEFT JOIN dbo.Customer c ON v.CustomerId = c.Id
    WHERE v.LastSessionDate IS NOT NULL
      AND v.LastSessionId IS NOT NULL
      AND (DATEDIFF(DAY, v.LastSessionDate, GETDATE()) > 3 OR CAST(v.LastSessionDate AS DATE) = CAST(GETDATE() AS DATE))
    GROUP BY 
        CASE
            WHEN v.ModuleIsConnected = 1 THEN 'Online'
            WHEN DATEDIFF(DAY, v.LastSessionDate, GETDATE()) > 7 THEN '> 7 days'
            WHEN DATEDIFF(DAY, v.LastSessionDate, GETDATE()) > 3 THEN '> 3 days'
            ELSE NULL
        END,
        c.DealerId, v.CustomerId, v.SiteId, v.DepartmentId
)
SELECT
    Id = NEWID(),
    LastOnlineStatus,
	DealerId,
    CustomerId,
    SiteId,
    DepartmentId,
    COALESCE(SUM(NumberOfVehicles), 0) AS NumberOfVehicles
FROM VehicleCounts
GROUP BY GROUPING SETS (
    (LastOnlineStatus, DealerId, CustomerId, SiteId, DepartmentId),  -- All levels
    (LastOnlineStatus, DealerId, CustomerId, SiteId),       -- Customer and Site
    (LastOnlineStatus, DealerId, CustomerId, DepartmentId),       -- Customer and Department
    (LastOnlineStatus, SiteId, DepartmentId),       -- Site and Department
    (LastOnlineStatus, DealerId, CustomerId),             -- Customer only
    (LastOnlineStatus, SiteId),             -- Site only
    (LastOnlineStatus, DepartmentId),             -- Department only
    (LastOnlineStatus)                  -- LastOnlineStatus only
);
GO

CREATE OR ALTER VIEW [dbo].[GeneralProductivityView] AS
SELECT
	'C547F788-B08E-4159-9B45-5D25195F7FCA' AS Id
GO

CREATE OR ALTER VIEW [dbo].[CurrentStatusCombinedView] AS
SELECT
	'B337F798-B08E-4159-9B45-5D25195F7FCA' AS Id
GO

CREATE OR ALTER VIEW [dbo].[ProficiencyCombinedView] AS
SELECT
	'B337F798-B08E-4159-9B45-5D25195F7FCB' AS Id
GO

CREATE OR ALTER PROCEDURE [dbo].[GetGeneralProductivityPerVehicle]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0,
    @MultiSearch NVARCHAR(4000) = NULL,
    @VehicleId UNIQUEIDENTIFIER = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @RowOffset INT = @PageIndex * @PageSize;
    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    -- Create table for vehicle hierarchy data
    CREATE TABLE #VehicleHierarchy (
        VehicleId UNIQUEIDENTIFIER PRIMARY KEY,
        DepartmentId UNIQUEIDENTIFIER,
        SiteId UNIQUEIDENTIFIER,
        CustomerId UNIQUEIDENTIFIER,
        DealerId UNIQUEIDENTIFIER,
        TimezoneOffset INT,
        HireNo NVARCHAR(50),
        SerialNo NVARCHAR(50),
        SiteName NVARCHAR(50),
        DepartmentName NVARCHAR(50)
    );

    -- Modified vehicle hierarchy query to match original filtering
    WITH ActiveSessions AS (
        -- Get vehicles with active sessions in the date range
        SELECT DISTINCT s.VehicleId
        FROM dbo.Session s
        INNER JOIN dbo.Vehicle v ON s.VehicleId = v.Id
        INNER JOIN dbo.Site site ON v.SiteId = site.Id
        INNER JOIN dbo.Department d ON v.DepartmentId = d.Id
        INNER JOIN dbo.Customer c ON site.CustomerId = c.Id
        INNER JOIN dbo.Timezone tz ON site.TimezoneId = tz.Id
        WHERE s.DriverId IS NOT NULL
        AND s.isVOR = 0
        AND DATEADD(HOUR, tz.UTCOffset, s.EndTime) BETWEEN DATEADD(HOUR, tz.UTCOffset, @ActualStartDate) AND DATEADD(HOUR, tz.UTCOffset, @ActualEndDate)
        AND (@CustomerId IS NULL OR c.Id = @CustomerId)
        AND (@SiteId IS NULL OR site.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
    )
    INSERT INTO #VehicleHierarchy
    SELECT DISTINCT
        v.Id AS VehicleId,
        d.Id AS DepartmentId,
        s.Id AS SiteId,
        c.Id AS CustomerId,
        de.Id AS DealerId,
        tz.UTCOffset AS TimezoneOffset,
        v.HireNo,
        v.SerialNo,
        s.Name AS SiteName,
        d.Name AS DepartmentName
    FROM dbo.Vehicle v
    INNER JOIN ActiveSessions act ON v.Id = act.VehicleId
    INNER JOIN dbo.Department d ON v.DepartmentId = d.Id
    INNER JOIN dbo.Site s ON d.SiteId = s.Id
    INNER JOIN dbo.Customer c ON s.CustomerId = c.Id
    INNER JOIN dbo.Dealer de ON c.DealerId = de.Id
    INNER JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
    WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
    AND (@SiteId IS NULL OR s.Id = @SiteId)
    AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
    AND (@VehicleId IS NULL OR v.Id = @VehicleId)
    AND (@MultiSearch IS NULL 
        OR v.HireNo LIKE '%' + @MultiSearch + '%' 
        OR s.Name LIKE '%' + @MultiSearch + '%' 
        OR d.Name LIKE '%' + @MultiSearch + '%');

    -- Create table for aggregated session data
    CREATE TABLE #SessionStats (
        VehicleId UNIQUEIDENTIFIER PRIMARY KEY,
        TotalDuration DECIMAL(10, 2),
        TotalSeatHours DECIMAL(10, 2),
        TotalHydraulicHours DECIMAL(10, 2),
        TotalTractionHours DECIMAL(10, 2)
    );

    -- Calculate metrics only for the filtered vehicles
    INSERT INTO #SessionStats
    SELECT 
        s.VehicleId,
        SUM(CASE WHEN io.Name = '0' THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalDuration,
        SUM(CASE WHEN io.Name IN ('4', 'SEAT') THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalSeatHours,
        SUM(CASE WHEN io.Name IN ('HYDR', 'HYDL', '1') THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalHydraulicHours,
        SUM(CASE WHEN io.Name IN ('TRACK', '2') THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalTractionHours
    FROM dbo.Session s
    INNER JOIN #VehicleHierarchy vh ON s.VehicleId = vh.VehicleId
    INNER JOIN dbo.SessionDetails sd ON s.Id = sd.SessionId
    INNER JOIN dbo.IOFIELD io ON io.Id = sd.IOFIELDId
    WHERE s.DriverId IS NOT NULL
    AND DATEADD(HOUR, vh.TimezoneOffset, s.EndTime) BETWEEN DATEADD(HOUR, vh.TimezoneOffset, @ActualStartDate) AND DATEADD(HOUR, vh.TimezoneOffset, @ActualEndDate)
    AND s.isVOR = 0
    GROUP BY s.VehicleId;

    -- Handle return total count
    IF @ReturnTotalCount = 1
    BEGIN
        SELECT COUNT(*) AS TotalRowCount FROM #VehicleHierarchy;
        RETURN;
    END

    -- Final result set
    SELECT 
        NEWID() AS Id,
        vh.VehicleId,
        vh.DealerId,
        CAST('C547F788-B08E-4159-9B45-5D25195F7FCA' AS UNIQUEIDENTIFIER) AS GeneralProductivityViewId,
        COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(ss.TotalDuration, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalDuration,
        COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(ss.TotalSeatHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalSeatHours,
        COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(ss.TotalHydraulicHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalHydraulicHours,
        COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(ss.TotalTractionHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalTractionHours,
        COALESCE(
            CASE 
                WHEN ss.TotalDuration > 0 THEN 
                    CONVERT(VARCHAR, CONVERT(DECIMAL(10, 2), ISNULL(ss.TotalSeatHours, 0) / ss.TotalDuration * 100)) + '%'
                ELSE '0%'
            END, 
            '0%'
        ) AS PercentageOfActivity
    FROM #VehicleHierarchy vh
    LEFT JOIN #SessionStats ss ON vh.VehicleId = ss.VehicleId
    ORDER BY vh.VehicleId
    OFFSET @RowOffset ROWS 
    FETCH NEXT @PageSize ROWS ONLY;

    -- Cleanup
    DROP TABLE #VehicleHierarchy;
    DROP TABLE #SessionStats;
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetGeneralProductivityPerDriver]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0,
    @MultiSearch NVARCHAR(4000) = NULL,
    @DriverId UNIQUEIDENTIFIER = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @RowOffset INT = @PageIndex * @PageSize;
    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    -- Create table for driver hierarchy data
    CREATE TABLE #DriverHierarchy (
        DriverId UNIQUEIDENTIFIER PRIMARY KEY,
        CustomerId UNIQUEIDENTIFIER,
        SiteId UNIQUEIDENTIFIER,
        DepartmentId UNIQUEIDENTIFIER,
        DealerId UNIQUEIDENTIFIER,
        TimezoneOffset INT,
        FullName NVARCHAR(101),
        SiteName NVARCHAR(50),
        DepartmentName NVARCHAR(50)
    );

    -- Get drivers with active sessions first
    WITH ActiveSessions AS (
        SELECT DISTINCT 
            s.DriverId
        FROM dbo.Session s
        INNER JOIN dbo.Driver d ON s.DriverId = d.Id
        INNER JOIN dbo.Person p ON d.Id = p.DriverId
        INNER JOIN dbo.Site site ON p.SiteId = site.Id
        INNER JOIN dbo.Timezone tz ON site.TimezoneId = tz.Id
        WHERE s.DriverId IS NOT NULL
        AND s.isVOR = 0
        AND DATEADD(HOUR, tz.UTCOffset, s.EndTime) BETWEEN DATEADD(HOUR, tz.UTCOffset, @ActualStartDate) AND DATEADD(HOUR, tz.UTCOffset, @ActualEndDate)
        AND (@CustomerId IS NULL OR p.CustomerId = @CustomerId)
        AND (@SiteId IS NULL OR p.SiteId = @SiteId)
        AND (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId)
        AND (@DriverId IS NULL OR s.DriverId = @DriverId)
    )
    -- Populate driver hierarchy with only active drivers
    INSERT INTO #DriverHierarchy
    SELECT DISTINCT
        d.Id AS DriverId,
        p.CustomerId,
        p.SiteId,
        p.DepartmentId,
        de.Id AS DealerId,
        tz.UTCOffset,
        p.FirstName + ' ' + p.LastName AS FullName,
        s.Name AS SiteName,
        dept.Name AS DepartmentName
    FROM dbo.Driver d
    INNER JOIN ActiveSessions act ON d.Id = act.DriverId
    INNER JOIN dbo.Person p ON d.Id = p.DriverId
    INNER JOIN dbo.Customer c ON p.CustomerId = c.Id
    INNER JOIN dbo.Dealer de ON c.DealerId = de.Id
    INNER JOIN dbo.Site s ON p.SiteId = s.Id
    INNER JOIN dbo.Department dept ON p.DepartmentId = dept.Id
    INNER JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
    WHERE (@CustomerId IS NULL OR p.CustomerId = @CustomerId)
    AND (@SiteId IS NULL OR p.SiteId = @SiteId)
    AND (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId)
    AND (@DriverId IS NULL OR d.Id = @DriverId)
    AND (@MultiSearch IS NULL 
        OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%'
        OR s.Name LIKE '%' + @MultiSearch + '%' 
        OR dept.Name LIKE '%' + @MultiSearch + '%');

    -- Create table for aggregated session data
    CREATE TABLE #SessionStats (
        DriverId UNIQUEIDENTIFIER PRIMARY KEY,
        TotalDuration DECIMAL(10, 2),
        TotalSeatHours DECIMAL(10, 2),
        TotalHydraulicHours DECIMAL(10, 2),
        TotalTractionHours DECIMAL(10, 2)
    );

    -- Calculate metrics only for filtered drivers
    INSERT INTO #SessionStats
    SELECT 
        s.DriverId,
        SUM(CASE WHEN io.Name = '0' THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalDuration,
        SUM(CASE WHEN io.Name IN ('4', 'SEAT') THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalSeatHours,
        SUM(CASE WHEN io.Name IN ('HYDR', 'HYDL', '1') THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalHydraulicHours,
        SUM(CASE WHEN io.Name IN ('TRACK', '2') THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalTractionHours
    FROM dbo.Session s
    INNER JOIN #DriverHierarchy dh ON s.DriverId = dh.DriverId
    INNER JOIN dbo.SessionDetails sd ON s.Id = sd.SessionId
    INNER JOIN dbo.IOFIELD io ON io.Id = sd.IOFIELDId
    WHERE s.DriverId IS NOT NULL
    AND DATEADD(HOUR, dh.TimezoneOffset, s.EndTime) BETWEEN DATEADD(HOUR, dh.TimezoneOffset, @ActualStartDate) AND DATEADD(HOUR, dh.TimezoneOffset, @ActualEndDate)
    AND s.isVOR = 0
    GROUP BY s.DriverId;

    -- Handle return total count
    IF @ReturnTotalCount = 1
    BEGIN
        SELECT COUNT(*) AS TotalRowCount FROM #DriverHierarchy;
        RETURN;
    END

    -- Final result set
    SELECT 
        NEWID() AS Id,
        dh.DriverId,
        dh.DealerId,
        CAST('C547F788-B08E-4159-9B45-5D25195F7FCA' AS UNIQUEIDENTIFIER) AS GeneralProductivityViewId,
        COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(ss.TotalDuration, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalDuration,
        COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(ss.TotalSeatHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalSeatHours,
        COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(ss.TotalHydraulicHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalHydraulicHours,
        COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(ss.TotalTractionHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalTractionHours,
        COALESCE(
            CASE 
                WHEN ss.TotalDuration > 0 THEN 
                    CONVERT(VARCHAR, CONVERT(DECIMAL(10, 2), ISNULL(ss.TotalSeatHours, 0) / ss.TotalDuration * 100)) + '%'
                ELSE '0%'
            END, 
            '0%'
        ) AS PercentageOfActivity
    FROM #DriverHierarchy dh
    LEFT JOIN #SessionStats ss ON dh.DriverId = ss.DriverId
    ORDER BY dh.DriverId
    OFFSET @RowOffset ROWS 
    FETCH NEXT @PageSize ROWS ONLY;

    -- Cleanup
    DROP TABLE #DriverHierarchy;
    DROP TABLE #SessionStats;
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetDetailedSession]
    @DriverId UNIQUEIDENTIFIER = NULL,
    @VehicleId UNIQUEIDENTIFIER = NULL,
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    -- Create a temporary table to replace the CTE
    CREATE TABLE #VehicleHireHistory (
        VehicleId UNIQUEIDENTIFIER,
        DepartmentId UNIQUEIDENTIFIER,
        SiteId UNIQUEIDENTIFIER,
        CustomerId UNIQUEIDENTIFIER,
        HireTime DATETIME,
        DehireTime DATETIME
    );

    -- Insert data into the temporary table
    INSERT INTO #VehicleHireHistory
    SELECT 
        vh.VehicleId,
        vh.DepartmentId,
        d.SiteId,
        s.CustomerId,
        vh.HireTime,
        vh.DehireTime
    FROM dbo.VehicleHireDehireHistory vh
    JOIN dbo.Department d ON d.Id = vh.DepartmentId
    JOIN dbo.Site s ON s.Id = d.SiteId
    JOIN dbo.Customer c ON c.Id = s.CustomerId
    WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
    AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate);

    IF @ReturnTotalCount = 1
    BEGIN
        SELECT COUNT(DISTINCT s.Id) AS TotalRowCount
        FROM dbo.Session AS s
        INNER JOIN dbo.Driver AS dr ON s.DriverId = dr.Id
        INNER JOIN dbo.Vehicle AS v ON s.VehicleId = v.Id
        LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
        INNER JOIN dbo.Site site ON ISNULL(vh.SiteId, v.SiteId) = site.Id
        INNER JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
        INNER JOIN dbo.Customer c ON ISNULL(vh.CustomerId, v.CustomerId) = c.Id
        INNER JOIN dbo.Timezone tz ON site.TimezoneId = tz.Id
        WHERE s.DriverId IS NOT NULL
          AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
          AND (@DriverId IS NULL OR s.DriverId = @DriverId)
          AND (@VehicleId IS NULL OR s.VehicleId = @VehicleId)
          AND (@CustomerId IS NULL OR c.Id = @CustomerId)
          AND (@SiteId IS NULL OR site.Id = @SiteId)
          AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
          AND s.isVOR = 0;  -- Only return records where isVOR is False
    END
    ELSE
    BEGIN
        DECLARE @RowOffset INT = @PageIndex * @PageSize;

        WITH DetailedCalculations AS (
            SELECT
                NEWID() AS Id,
                s.Id AS SessionId,
                s.DriverId,
                s.VehicleId,
                SUM(CASE WHEN io.Name = '0' THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalDuration,
                SUM(CASE WHEN io.Name = '4' OR io.Name = 'SEAT' THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalSeatHours,
                SUM(CASE WHEN io.Name = 'HYDR' OR io.Name = 'HYDL' OR io.Name = '1' THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalHydraulicHours,
                SUM(CASE WHEN io.Name = 'TRACK' OR io.Name = '2' THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalTractionHours,
                DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.StartTime) AS TimezoneAdjustedStartDateTime,
                DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) AS TimezoneAdjustedEndDateTime
            FROM dbo.Session AS s
            INNER JOIN dbo.SessionDetails AS sd ON s.Id = sd.SessionId
            INNER JOIN dbo.IOFIELD AS io ON io.Id = sd.IOFIELDId
            INNER JOIN dbo.Vehicle AS v ON s.VehicleId = v.Id
            LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
            INNER JOIN dbo.Site site ON ISNULL(vh.SiteId, v.SiteId) = site.Id
            INNER JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
            INNER JOIN dbo.Customer c ON ISNULL(vh.CustomerId, v.CustomerId) = c.Id
            INNER JOIN dbo.Driver dr ON s.DriverId = dr.Id
            INNER JOIN dbo.Timezone tz ON site.TimezoneId = tz.Id
            WHERE s.DriverId IS NOT NULL
              AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
              AND (@DriverId IS NULL OR s.DriverId = @DriverId)
              AND (@VehicleId IS NULL OR s.VehicleId = @VehicleId)
              AND (@CustomerId IS NULL OR c.Id = @CustomerId)
              AND (@SiteId IS NULL OR site.Id = @SiteId)
              AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
              AND s.isVOR = 0  -- Only return records where isVOR is False
            GROUP BY s.Id, s.DriverId, s.VehicleId, s.StartTime, s.EndTime, tz.UTCOffset
        )
        SELECT
            Id,
            SessionId,
            DriverId,
            VehicleId,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TotalDuration, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalDuration,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TotalSeatHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalSeatHours,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TotalHydraulicHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalHydraulicHours,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TotalTractionHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalTractionHours,
            TimezoneAdjustedStartDateTime,
            TimezoneAdjustedEndDateTime
        FROM DetailedCalculations
        ORDER BY SessionId
        OFFSET @RowOffset ROWS FETCH NEXT @PageSize ROWS ONLY;
    END
    -- Drop the temporary table
    DROP TABLE #VehicleHireHistory;
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetImpactFrequencyPerTimeSlot]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @ImpactLevel INT = NULL,
    @MultiSearch NVARCHAR(4000) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    -- Create index for the temp table to improve join performance
    CREATE TABLE #ValidVehicles 
    (
        VehicleId UNIQUEIDENTIFIER PRIMARY KEY,
        TimezoneOffset INT,
        DealerId UNIQUEIDENTIFIER
    );

    -- Get valid vehicles based on filters - reduces data in main query
    INSERT INTO #ValidVehicles (VehicleId, TimezoneOffset, DealerId)
    SELECT DISTINCT 
        v.Id,
        tz.UTCOffset,
        de.Id
    FROM dbo.Vehicle v
    INNER JOIN dbo.Site s ON v.SiteId = s.Id
    INNER JOIN dbo.Department d ON v.DepartmentId = d.Id
    INNER JOIN dbo.Customer c ON s.CustomerId = c.Id
    INNER JOIN dbo.Dealer de ON c.DealerId = de.Id
    INNER JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
    WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
    AND (@SiteId IS NULL OR s.Id = @SiteId)
    AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
    AND (@MultiSearch IS NULL 
        OR v.HireNo LIKE '%' + @MultiSearch + '%' 
        OR s.Name LIKE '%' + @MultiSearch + '%' 
        OR d.Name LIKE '%' + @MultiSearch + '%');

    -- Use temp table for impact aggregation
    CREATE TABLE #ImpactCounts 
    (
        Hour INT,
        RedImpacts INT,
        AmberImpacts INT
    );

    -- Calculate impact counts for valid vehicles with proper impact level filtering
    INSERT INTO #ImpactCounts (Hour, RedImpacts, AmberImpacts)
    SELECT 
        DATEPART(HOUR, DATEADD(HOUR, vv.TimezoneOffset, i.ImpactDateTime)),
        COUNT(CASE 
            WHEN i.ShockValue >= i.Threshold * 10 
            AND (@ImpactLevel IS NULL OR @ImpactLevel IN (0, 1))
            THEN 1 
        END),
        COUNT(CASE 
            WHEN i.ShockValue >= i.Threshold * 5 
            AND i.ShockValue < i.Threshold * 10 
            AND (@ImpactLevel IS NULL OR @ImpactLevel = 1)
            THEN 1 
        END)
    FROM dbo.Impact i
    INNER JOIN dbo.Session s ON i.SessionId = s.Id
    INNER JOIN #ValidVehicles vv ON s.VehicleId = vv.VehicleId
    WHERE i.Threshold != 0
    AND DATEADD(HOUR, vv.TimezoneOffset, i.ImpactDateTime) BETWEEN DATEADD(HOUR, vv.TimezoneOffset, @ActualStartDate) AND DATEADD(HOUR, vv.TimezoneOffset, @ActualEndDate)
    GROUP BY DATEPART(HOUR, DATEADD(HOUR, vv.TimezoneOffset, i.ImpactDateTime))
    HAVING 
        (COUNT(CASE 
            WHEN i.ShockValue >= i.Threshold * 10 
            AND (@ImpactLevel IS NULL OR @ImpactLevel IN (0, 1))
            THEN 1 
        END) > 0)
        OR 
        (COUNT(CASE 
            WHEN i.ShockValue >= i.Threshold * 5 
            AND i.ShockValue < i.Threshold * 10 
            AND (@ImpactLevel IS NULL OR @ImpactLevel = 1)
            THEN 1 
        END) > 0);

    -- Get DealerId for final result
    DECLARE @DealerId UNIQUEIDENTIFIER = (SELECT TOP 1 DealerId FROM #ValidVehicles);

    -- Return final results
    SELECT 
        NEWID() AS Id,
        CONVERT(VARCHAR, ic.Hour) + ':00' + 
        CASE WHEN ic.Hour < 12 THEN 'am' ELSE 'pm' END AS TimeSlot,
        ic.RedImpacts AS NumberOfRedImpacts,
        ic.AmberImpacts AS NumberOfAmberImpacts,
        @DealerId AS DealerId
    FROM #ImpactCounts ic
    ORDER BY ic.Hour;

    -- Cleanup
    DROP TABLE #ValidVehicles;
    DROP TABLE #ImpactCounts;
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetImpactFrequencyPerWeekMonth]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @ImpactLevel INT = NULL,
    @MultiSearch NVARCHAR(4000) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    -- Create index for the temp table to improve join performance
    CREATE TABLE #ValidVehicles 
    (
        VehicleId UNIQUEIDENTIFIER PRIMARY KEY,
        TimezoneOffset INT,
        DealerId UNIQUEIDENTIFIER
    );

    -- Get valid vehicles based on filters - reduces data in main query
    INSERT INTO #ValidVehicles (VehicleId, TimezoneOffset, DealerId)
    SELECT DISTINCT 
        v.Id,
        tz.UTCOffset,
        de.Id
    FROM dbo.Vehicle v
    INNER JOIN dbo.Site s ON v.SiteId = s.Id
    INNER JOIN dbo.Department d ON v.DepartmentId = d.Id
    INNER JOIN dbo.Customer c ON s.CustomerId = c.Id
    INNER JOIN dbo.Dealer de ON c.DealerId = de.Id
    INNER JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
    LEFT JOIN dbo.Driver dr ON dr.CustomerId = c.Id
    LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
    LEFT JOIN (
        SELECT DISTINCT VehicleId
        FROM dbo.VehicleHireDehireHistory vh
        WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
        AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate)
    ) vh ON v.Id = vh.VehicleId
    WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
    AND (@SiteId IS NULL OR s.Id = @SiteId)
    AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
    AND (@MultiSearch IS NULL 
        OR v.HireNo LIKE '%' + @MultiSearch + '%' 
        OR s.Name LIKE '%' + @MultiSearch + '%' 
        OR d.Name LIKE '%' + @MultiSearch + '%'
        OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%');

    -- Use temp table for impact aggregation
    CREATE TABLE #ImpactCounts 
    (
        WeekNumber INT,
        DealerId UNIQUEIDENTIFIER,
        RedImpacts INT,
        AmberImpacts INT
    );

    -- Calculate impact counts for valid vehicles
    INSERT INTO #ImpactCounts (WeekNumber, DealerId, RedImpacts, AmberImpacts)
    SELECT 
        DATEPART(WEEK, DATEADD(HOUR, vv.TimezoneOffset, i.ImpactDateTime)),
        vv.DealerId,
        COUNT(CASE WHEN i.ShockValue >= i.Threshold * 10 THEN 1 END),
        CASE 
            WHEN @ImpactLevel IS NULL OR @ImpactLevel IN (1, 2) 
            THEN COUNT(CASE 
                WHEN i.ShockValue >= i.Threshold * 5 
                AND i.ShockValue < i.Threshold * 10 
                THEN 1 
            END)
            ELSE 0 
        END
    FROM dbo.Impact i
    INNER JOIN dbo.Session s ON i.SessionId = s.Id
    INNER JOIN #ValidVehicles vv ON s.VehicleId = vv.VehicleId
    WHERE i.Threshold != 0
    AND DATEADD(HOUR, vv.TimezoneOffset, i.ImpactDateTime) BETWEEN DATEADD(HOUR, vv.TimezoneOffset, @ActualStartDate) AND DATEADD(HOUR, vv.TimezoneOffset, @ActualEndDate)
    GROUP BY 
        DATEPART(WEEK, DATEADD(HOUR, vv.TimezoneOffset, i.ImpactDateTime)),
        vv.DealerId
    HAVING COUNT(CASE WHEN i.ShockValue >= i.Threshold * 10 THEN 1 END) > 0
        OR COUNT(CASE 
            WHEN (@ImpactLevel IS NULL OR @ImpactLevel IN (1, 2))
                AND i.ShockValue >= i.Threshold * 5 
                AND i.ShockValue < i.Threshold * 10 
            THEN 1 
        END) > 0;

    -- Return final results
    SELECT 
        NEWID() AS Id,
        'w' + CONVERT(VARCHAR(10), ic.WeekNumber) AS MonthWeek,
        ic.RedImpacts AS NumberOfRedImpacts,
        ic.AmberImpacts AS NumberOfAmberImpacts,
        ic.DealerId
    FROM #ImpactCounts ic
    ORDER BY ic.WeekNumber;

    -- Cleanup
    DROP TABLE #ValidVehicles;
    DROP TABLE #ImpactCounts;
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetImpactFrequencyPerWeekDay]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @ImpactLevel INT = NULL,
    @MultiSearch NVARCHAR(4000) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    -- Create index for the temp table to improve join performance
    CREATE TABLE #ValidVehicles 
    (
        VehicleId UNIQUEIDENTIFIER PRIMARY KEY,
        TimezoneOffset INT,
        DealerId UNIQUEIDENTIFIER
    );

    -- Get valid vehicles based on filters - reduces data in main query
    INSERT INTO #ValidVehicles (VehicleId, TimezoneOffset, DealerId)
    SELECT DISTINCT 
        v.Id,
        tz.UTCOffset,
        de.Id
    FROM dbo.Vehicle v
    INNER JOIN dbo.Site s ON v.SiteId = s.Id
    INNER JOIN dbo.Department d ON v.DepartmentId = d.Id
    INNER JOIN dbo.Customer c ON s.CustomerId = c.Id
    INNER JOIN dbo.Dealer de ON c.DealerId = de.Id
    INNER JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
    LEFT JOIN dbo.Driver dr ON dr.CustomerId = c.Id
    LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
    LEFT JOIN (
        SELECT DISTINCT VehicleId
        FROM dbo.VehicleHireDehireHistory vh
        WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
        AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate)
    ) vh ON v.Id = vh.VehicleId
    WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
    AND (@SiteId IS NULL OR s.Id = @SiteId)
    AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
    AND (@MultiSearch IS NULL 
        OR v.HireNo LIKE '%' + @MultiSearch + '%' 
        OR s.Name LIKE '%' + @MultiSearch + '%' 
        OR d.Name LIKE '%' + @MultiSearch + '%'
        OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%');

    -- Create temp table for weekday sorting
    CREATE TABLE #WeekDays (
        WeekDayName VARCHAR(10),
        SortOrder INT PRIMARY KEY
    );

    INSERT INTO #WeekDays (WeekDayName, SortOrder) VALUES
    ('Sunday', 1),
    ('Monday', 2),
    ('Tuesday', 3),
    ('Wednesday', 4),
    ('Thursday', 5),
    ('Friday', 6),
    ('Saturday', 7);

    -- Use temp table for impact aggregation
    CREATE TABLE #ImpactCounts (
        WeekDayName VARCHAR(10),
        DealerId UNIQUEIDENTIFIER,
        RedImpacts INT,
        AmberImpacts INT
    );

    -- Calculate impact counts for valid vehicles
    INSERT INTO #ImpactCounts (WeekDayName, DealerId, RedImpacts, AmberImpacts)
    SELECT 
        DATENAME(WEEKDAY, DATEADD(HOUR, vv.TimezoneOffset, i.ImpactDateTime)),
        vv.DealerId,
        SUM(CASE WHEN i.ShockValue >= i.Threshold * 10 THEN 1 ELSE 0 END),
        CASE 
            WHEN @ImpactLevel IS NULL OR @ImpactLevel IN (1, 2) 
            THEN SUM(CASE 
                WHEN i.ShockValue >= i.Threshold * 5 
                AND i.ShockValue < i.Threshold * 10 
                THEN 1 ELSE 0 
            END)
            ELSE 0 
        END
    FROM dbo.Impact i
    INNER JOIN dbo.Session s ON i.SessionId = s.Id
    INNER JOIN #ValidVehicles vv ON s.VehicleId = vv.VehicleId
    WHERE i.Threshold != 0
    AND DATEADD(HOUR, vv.TimezoneOffset, i.ImpactDateTime) BETWEEN DATEADD(HOUR, vv.TimezoneOffset, @ActualStartDate) AND DATEADD(HOUR, vv.TimezoneOffset, @ActualEndDate)
    GROUP BY 
        DATENAME(WEEKDAY, DATEADD(HOUR, vv.TimezoneOffset, i.ImpactDateTime)),
        vv.DealerId
    HAVING SUM(CASE WHEN i.ShockValue >= i.Threshold * 10 THEN 1 ELSE 0 END) > 0
        OR ((@ImpactLevel IS NULL OR @ImpactLevel IN (1, 2))
            AND SUM(CASE 
                WHEN i.ShockValue >= i.Threshold * 5 
                AND i.ShockValue < i.Threshold * 10 
                THEN 1 ELSE 0 
            END) > 0);

    -- Return final results with proper weekday sorting
    SELECT 
        NEWID() AS Id,
        ic.WeekDayName AS Weekday,
        ic.RedImpacts AS NumberOfRedImpacts,
        ic.AmberImpacts AS NumberOfAmberImpacts,
        ic.DealerId
    FROM #ImpactCounts ic
    INNER JOIN #WeekDays wd ON ic.WeekDayName = wd.WeekDayName
    ORDER BY wd.SortOrder;

    -- Cleanup
    DROP TABLE #ValidVehicles;
    DROP TABLE #WeekDays;
    DROP TABLE #ImpactCounts;
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetAllImpacts]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0,
    @ImpactLevel INT = NULL,
    @MultiSearch NVARCHAR(4000) = NULL,
    @ImpactId UNIQUEIDENTIFIER = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @RowOffset INT = @PageIndex * @PageSize;
    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    -- Create filtered impacts first
    CREATE TABLE #FilteredImpacts 
    (
        Id UNIQUEIDENTIFIER PRIMARY KEY,
        SessionId UNIQUEIDENTIFIER,
        ShockValue FLOAT,
        Threshold FLOAT,
        ImpactDateTime DATETIME,
        ImpactType INT,
        GForceLevel VARCHAR(20)
    );

    -- Get base impacts that meet threshold criteria
    INSERT INTO #FilteredImpacts
    SELECT 
        imp.Id,
        imp.SessionId,
        imp.ShockValue,
        imp.Threshold,
        imp.ImpactDateTime,
        CASE 
            WHEN imp.ShockValue >= imp.Threshold * 5 AND imp.ShockValue < imp.Threshold * 10 THEN 0
            WHEN imp.ShockValue > 10 * imp.Threshold THEN 1
            WHEN imp.ShockValue >= imp.Threshold AND imp.ShockValue < imp.Threshold * 5 THEN 2
            ELSE NULL
        END,
        CONVERT(VARCHAR(20), CONVERT(DECIMAL(18, 1), 0.00388 * SQRT(imp.ShockValue))) + 'g'
    FROM dbo.Impact imp
    WHERE imp.Threshold != 0
    AND imp.ShockValue >= imp.Threshold
    AND (@ImpactId IS NULL OR imp.Id = @ImpactId)
    AND (
        (@ImpactLevel = 0 AND imp.ShockValue > 10 * imp.Threshold) OR
        (@ImpactLevel = 1 AND (imp.ShockValue > 10 * imp.Threshold OR (imp.ShockValue >= 5 * imp.Threshold AND imp.ShockValue < 10 * imp.Threshold))) OR
        (@ImpactLevel = 2 OR @ImpactLevel IS NULL)
    );

    -- Create index on the temp table
    CREATE NONCLUSTERED INDEX IX_FilteredImpacts_SessionId ON #FilteredImpacts(SessionId);

    -- Handle return total count efficiently
    IF @ReturnTotalCount = 1
    BEGIN
        SELECT COUNT(DISTINCT fi.Id) AS TotalRowCount
        FROM #FilteredImpacts fi
        INNER JOIN dbo.Session sess ON sess.Id = fi.SessionId
        INNER JOIN dbo.Vehicle v ON v.Id = sess.VehicleId
        INNER JOIN dbo.Site s ON v.SiteId = s.Id
        INNER JOIN dbo.Department d ON v.DepartmentId = d.Id
        INNER JOIN dbo.Customer c ON s.CustomerId = c.Id
        INNER JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
        LEFT JOIN dbo.Driver dr ON dr.Id = sess.DriverId
        LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
        WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
        AND (@SiteId IS NULL OR s.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
        AND DATEADD(HOUR, tz.UTCOffset, fi.ImpactDateTime) BETWEEN DATEADD(HOUR, tz.UTCOffset,@ActualStartDate) AND DATEADD(HOUR, tz.UTCOffset, @ActualEndDate)
        AND (@MultiSearch IS NULL 
            OR v.HireNo LIKE '%' + @MultiSearch + '%' 
            OR s.Name LIKE '%' + @MultiSearch + '%'
            OR d.Name LIKE '%' + @MultiSearch + '%'
            OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%');
    END
    ELSE
    BEGIN
        SELECT
            fi.Id,
            fi.Id AS ImpactId,
            fi.ImpactType,
            fi.GForceLevel,
            fi.ShockValue,
            fi.Threshold,
            DATEADD(HOUR, tz.UTCOffset, fi.ImpactDateTime) AS TimezoneAdjustedImpactDatetime,
            de.Id AS DealerId
        FROM #FilteredImpacts fi
        INNER JOIN dbo.Session sess ON sess.Id = fi.SessionId
        INNER JOIN dbo.Vehicle v ON v.Id = sess.VehicleId
        INNER JOIN dbo.Site s ON v.SiteId = s.Id
        INNER JOIN dbo.Department d ON v.DepartmentId = d.Id
        INNER JOIN dbo.Customer c ON s.CustomerId = c.Id
        INNER JOIN dbo.Dealer de ON c.DealerId = de.Id
        INNER JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
        LEFT JOIN dbo.Driver dr ON dr.Id = sess.DriverId
        LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
        WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
        AND (@SiteId IS NULL OR s.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
        AND DATEADD(HOUR, tz.UTCOffset, fi.ImpactDateTime) BETWEEN DATEADD(HOUR, tz.UTCOffset, @ActualStartDate) AND DATEADD(HOUR, tz.UTCOffset, @ActualEndDate)
        AND (@MultiSearch IS NULL 
            OR v.HireNo LIKE '%' + @MultiSearch + '%' 
            OR s.Name LIKE '%' + @MultiSearch + '%'
            OR d.Name LIKE '%' + @MultiSearch + '%'
            OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%')
        ORDER BY DATEADD(HOUR, tz.UTCOffset, fi.ImpactDateTime) DESC
        OFFSET @RowOffset ROWS
        FETCH NEXT @PageSize ROWS ONLY;
    END;

    DROP TABLE #FilteredImpacts;
END;
GO

-- TODO: Bring Back Timezone Adjustment once vehicle sends UTC time
CREATE OR ALTER PROCEDURE [dbo].[GetChecklistStatus]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @ResultType INT = NULL,
    @MultiSearch NVARCHAR(4000) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());
    DECLARE @TotalChecklists FLOAT;
    DECLARE @CompletedChecklists FLOAT;

    -- Create a temporary table to replace the CTE
    CREATE TABLE #VehicleHireHistory (
        VehicleId UNIQUEIDENTIFIER,
        DepartmentId UNIQUEIDENTIFIER,
        SiteId UNIQUEIDENTIFIER,
        CustomerId UNIQUEIDENTIFIER,
        HireTime DATETIME,
        DehireTime DATETIME
    );

    -- Insert data into the temporary table
    INSERT INTO #VehicleHireHistory
    SELECT 
        vh.VehicleId,
        vh.DepartmentId,
        d.SiteId,
        s.CustomerId,
        vh.HireTime,
        vh.DehireTime
    FROM dbo.VehicleHireDehireHistory vh
    JOIN dbo.Department d ON d.Id = vh.DepartmentId
    JOIN dbo.Site s ON s.Id = d.SiteId
    JOIN dbo.Customer c ON c.Id = s.CustomerId
    WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
    AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate);

IF @ResultType = 0
BEGIN
    -- Total number of checklists
    SELECT @TotalChecklists = COUNT(*) FROM dbo.ChecklistResult cr
    LEFT JOIN dbo.ChecklistDetail cd ON cr.Id = cd.ChecklistResultId
    LEFT JOIN dbo.Session sess ON sess.Id = cr.SessionId1
    LEFT JOIN dbo.Vehicle v ON v.Id = sess.VehicleId
    LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id
    LEFT JOIN dbo.Driver dr ON dr.Id = sess.DriverId
    LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
    LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
    LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
    LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
    LEFT JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
    WHERE cr.EndTime IS NOT NULL 
    AND cr.EndTime <> cr.StartTime -- Added condition to exclude where StartTime equals EndTime
    AND cd.Failed = 1
    AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), cr.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
    AND (@CustomerId IS NULL OR c.Id = @CustomerId)
    AND (@SiteId IS NULL OR s.Id = @SiteId)
    AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
    AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' 
        OR d.Name LIKE '%' + @MultiSearch + '%' OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%');

    -- Handle case where there are no results
    IF @TotalChecklists = 0
    BEGIN
        SELECT NEWID() AS Id, 0 AS Number, 0 AS ChecklistStatus
        UNION ALL
        SELECT NEWID() AS Id, 0 AS Number, 1 AS ChecklistStatus
        UNION ALL
        SELECT NEWID() AS Id, 0 AS Number, 2 AS ChecklistStatus;
        
        -- Clean up and exit
        DROP TABLE #VehicleHireHistory;
        RETURN;
    END

    -- Emit results for Complete and Incomplete
    SELECT NEWID() AS Id, 100 AS Number, 0 AS ChecklistStatus
    UNION ALL
    SELECT NEWID() AS Id, 0 AS Number, 1 AS ChecklistStatus
    UNION ALL
    SELECT
        NEWID() AS Id,
        CASE 
            WHEN @TotalChecklists = 0 THEN 0
            ELSE CAST((COUNT(*) / @TotalChecklists * 100) AS INT)
        END AS Number,
        2 AS ChecklistStatus
    FROM
        dbo.ChecklistResult cr
        LEFT JOIN dbo.ChecklistDetail cd ON cr.Id = cd.ChecklistResultId
        LEFT JOIN dbo.PreOperationalChecklist poc ON cd.PreOperationalChecklistId = poc.Id
        LEFT JOIN dbo.Session sess ON sess.Id = cr.SessionId1
        LEFT JOIN dbo.Vehicle v ON v.Id = sess.VehicleId
        LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id
        LEFT JOIN dbo.Driver dr ON dr.Id = sess.DriverId
        LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
        LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
        LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
        LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
        LEFT JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
    WHERE poc.Critical = 1 
    AND cd.Answer != poc.ExpectedAnswer 
    AND cr.EndTime IS NOT NULL 
    AND cr.EndTime <> cr.StartTime -- Added condition to exclude where StartTime equals EndTime
    AND cd.Failed = 1
    AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), cr.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
    AND (@CustomerId IS NULL OR c.Id = @CustomerId)
    AND (@SiteId IS NULL OR s.Id = @SiteId)
    AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
    AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' 
        OR d.Name LIKE '%' + @MultiSearch + '%' OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%');
END
ELSE IF @ResultType = 1
BEGIN
    -- Critical Failed Issues
    SELECT
        NEWID() AS Id,
        0 AS Number,
        0 AS ChecklistStatus
    UNION ALL
    SELECT
        NEWID() AS Id,
        0 AS Number,
        1 AS ChecklistStatus
    UNION ALL
    SELECT
        NEWID() AS Id,
        100 AS Number,
        2 AS ChecklistStatus;
END
ELSE
BEGIN
    -- Total number of checklists
    SELECT @TotalChecklists = COUNT(*) FROM dbo.ChecklistResult cr
    LEFT JOIN dbo.Session sess ON sess.Id = cr.SessionId1
    LEFT JOIN dbo.Vehicle v ON v.Id = sess.VehicleId
    LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id
    LEFT JOIN dbo.Driver dr ON dr.Id = sess.DriverId
    LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
    LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
    LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
    LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
    LEFT JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
    WHERE DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), cr.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
    AND (@CustomerId IS NULL OR c.Id = @CustomerId)
    AND (@SiteId IS NULL OR s.Id = @SiteId)
    AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
    AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' 
        OR d.Name LIKE '%' + @MultiSearch + '%' OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%');

    -- Handle case where there are no results
    IF @TotalChecklists = 0
    BEGIN
        SELECT NEWID() AS Id, 0 AS Number, 0 AS ChecklistStatus
        UNION ALL
        SELECT NEWID() AS Id, 0 AS Number, 1 AS ChecklistStatus
        UNION ALL
        SELECT NEWID() AS Id, 0 AS Number, 2 AS ChecklistStatus;
        
        -- Clean up and exit
        DROP TABLE #VehicleHireHistory;
        RETURN;
    END

    -- Number of completed checklists
    SELECT @CompletedChecklists = COUNT(*) FROM dbo.ChecklistResult cr
    LEFT JOIN dbo.Session sess ON sess.Id = cr.SessionId1
    LEFT JOIN dbo.Vehicle v ON v.Id = sess.VehicleId
    LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id
    LEFT JOIN dbo.Driver dr ON dr.Id = sess.DriverId
    LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
    LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
    LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
    LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
    LEFT JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
    WHERE cr.EndTime IS NOT NULL
    AND cr.EndTime <> cr.StartTime -- Added condition to exclude where StartTime equals EndTime
    AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), cr.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
    AND (@CustomerId IS NULL OR c.Id = @CustomerId)
    AND (@SiteId IS NULL OR s.Id = @SiteId)
    AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
    AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' 
        OR d.Name LIKE '%' + @MultiSearch + '%' OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%');

    -- Emit results with safe division
    SELECT NEWID() AS Id, 
           CASE 
               WHEN @TotalChecklists = 0 THEN 0 
               ELSE CAST((@CompletedChecklists / @TotalChecklists * 100) AS INT)
           END AS Number, 
           0 AS ChecklistStatus
    UNION ALL
    SELECT NEWID() AS Id, 
           CASE 
               WHEN @TotalChecklists = 0 THEN 0 
               ELSE 100 - CAST((@CompletedChecklists / @TotalChecklists * 100) AS INT)
           END AS Number, 
           1 AS ChecklistStatus
    UNION ALL
    SELECT
        NEWID() AS Id,
        CASE 
            WHEN @TotalChecklists = 0 THEN 0
            ELSE CAST((CAST(COUNT(DISTINCT CASE WHEN poc.Critical = 1 AND cd.Answer != poc.ExpectedAnswer THEN cr.Id ELSE NULL END) AS FLOAT) / @TotalChecklists * 100) AS INT)
        END AS Number,
        2 AS ChecklistStatus
    FROM
        dbo.ChecklistResult cr
        LEFT JOIN dbo.ChecklistDetail cd ON cr.Id = cd.ChecklistResultId
        LEFT JOIN dbo.PreOperationalChecklist poc ON cd.PreOperationalChecklistId = poc.Id
        LEFT JOIN dbo.Session sess ON sess.Id = cr.SessionId1
        LEFT JOIN dbo.Vehicle v ON v.Id = sess.VehicleId
        LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id
        LEFT JOIN dbo.Driver dr ON dr.Id = sess.DriverId
        LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
        LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
        LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
        LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
    WHERE
        cr.StartTime BETWEEN @ActualStartDate AND @ActualEndDate
        AND (@CustomerId IS NULL OR c.Id = @CustomerId)
        AND (@SiteId IS NULL OR s.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
        AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' 
            OR d.Name LIKE '%' + @MultiSearch + '%' OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%');
END

    -- Drop the temporary table to clean up
    DROP TABLE #VehicleHireHistory;
END;
GO

-- TODO: Bring Back Timezone Adjustment once vehicle sends UTC time
CREATE OR ALTER PROCEDURE [dbo].[GetChecklistResultByType]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @ResultType INT = NULL,  -- Added this new parameter
    @MultiSearch NVARCHAR(4000) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    -- CTE to identify checklists with any failed critical questions
    WITH VehicleHireHistory AS (
        SELECT 
            vh.VehicleId,
            vh.DepartmentId,
            d.SiteId,
            s.CustomerId,
            vh.HireTime,
            vh.DehireTime
        FROM dbo.VehicleHireDehireHistory vh
        JOIN dbo.Department d ON d.Id = vh.DepartmentId
        JOIN dbo.Site s ON s.Id = d.SiteId
        JOIN dbo.Customer c ON c.Id = s.CustomerId
        WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
        AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate)
    ),
    CriticalFailures AS (
        SELECT DISTINCT
            cd.ChecklistResultId
        FROM
            dbo.ChecklistDetail cd
            INNER JOIN dbo.PreOperationalChecklist poc ON poc.Id = cd.PreOperationalChecklistId
        WHERE
            poc.Critical = 1 AND cd.Answer != poc.ExpectedAnswer
    )
    SELECT
        NEWID() AS Id,
        de.Id AS DealerId,
        DATEPART(year, cr.StartTime) AS Year,
        DATEPART(month, cr.StartTime) AS Month,
        DATEPART(day, CONVERT(varchar(19), DATEADD(HOUR, tz.UTCOffset, cr.StartTime), 120)) AS Day,
        -- Count as incomplete if EndTime is NULL OR StartTime equals EndTime
        COUNT(CASE WHEN cr.EndTime IS NULL OR cr.StartTime = cr.EndTime THEN 1 ELSE NULL END) AS NumberOfIncompletedChecklists,
        -- Count as completed only if EndTime is NOT NULL AND StartTime is different from EndTime
        COUNT(CASE WHEN cr.EndTime IS NOT NULL AND cr.StartTime <> cr.EndTime THEN 1 ELSE NULL END) AS NumberOfCompletedChecklists,
        DATEPART(year, cr.StartTime) * 368 + DATEPART(month, cr.StartTime) * 31 + DATEPART(day, CONVERT(varchar(19), DATEADD(HOUR, tz.UTCOffset, cr.StartTime), 120)) AS [Order],
        COUNT(DISTINCT CASE WHEN cfc.ChecklistResultId IS NOT NULL THEN cr.Id ELSE NULL END) AS NumberOfCriticalFailedChecklists
    FROM
        dbo.ChecklistResult cr
        LEFT JOIN dbo.Session sess ON sess.Id = cr.SessionId1
        LEFT JOIN dbo.Vehicle v ON v.Id = sess.VehicleId
        LEFT JOIN VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use hire/dehire history
        LEFT JOIN dbo.Driver dr ON dr.Id = sess.DriverId
        LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
        LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id  -- Adjust for department history
        LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id  -- Adjust for site history
        LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id  -- Adjust for customer history
        LEFT JOIN dbo.Dealer de ON c.DealerId = de.Id
        LEFT JOIN CriticalFailures cfc ON cr.Id = cfc.ChecklistResultId
        LEFT JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
    WHERE
        DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), cr.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
        AND (@CustomerId IS NULL OR c.Id = @CustomerId)
        AND (@SiteId IS NULL OR s.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
        AND (
            @ResultType IS NULL OR 
            (@ResultType = 0 AND cr.EndTime IS NOT NULL AND cr.StartTime <> cr.EndTime AND EXISTS (
                SELECT 1 FROM dbo.ChecklistDetail cd WHERE cd.ChecklistResultId = cr.Id AND cd.Failed = 1
            )) OR 
            (@ResultType = 1 AND cr.EndTime IS NOT NULL AND cr.StartTime <> cr.EndTime AND cfc.ChecklistResultId IS NOT NULL) OR
            (@ResultType = 2)
        )
        AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' 
            OR d.Name LIKE '%' + @MultiSearch + '%' OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%')
    GROUP BY
        de.Id, c.Id, s.Id, d.Id,
        DATEPART(year, cr.StartTime), DATEPART(month, cr.StartTime), DATEPART(day, CONVERT(varchar(19), DATEADD(HOUR, tz.UTCOffset, cr.StartTime), 120)),
        DATEPART(year, cr.StartTime) * 368 + DATEPART(month, cr.StartTime) * 31 + DATEPART(day, CONVERT(varchar(19), DATEADD(HOUR, tz.UTCOffset, cr.StartTime), 120))
		-- DATEPART(year, cr.StartTime), DATEPART(month, cr.StartTime), DATEPART(day, CONVERT(varchar(19), cr.StartTime, 120)),
		-- DATEPART(year, cr.StartTime) * 368 + DATEPART(month, cr.StartTime) * 31 + DATEPART(day, CONVERT(varchar(19), cr.StartTime, 120))
    ORDER BY [Order]
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetAllChecklistResults]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0,
    @ResultType INT = NULL,  -- Added this new parameter
    @ChecklistResultId UNIQUEIDENTIFIER = NULL,  -- New parameter for ChecklistResultId
    @MultiSearch NVARCHAR(4000) = NULL  
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    IF @ChecklistResultId IS NOT NULL
    BEGIN
        -- Return the row corresponding to the provided ChecklistResultId
        SELECT
            NEWID() AS Id,
            cr.Id AS ChecklistResultId,
            CASE 
                WHEN cr.EndTime IS NULL OR cr.StartTime = cr.EndTime THEN 0 
                ELSE 1
            END AS CheckComplete,
            CASE 
                WHEN cr.EndTime IS NULL OR cr.StartTime = cr.EndTime THEN '00:00:00'
                ELSE CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, DATEDIFF(SECOND, cr.StartTime, cr.EndTime), 0), 'HH:mm:ss'))
            END AS CompletionTime,
			DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), cr.StartTime) AS TimezoneAdjustedChecklistStartTime,
            CASE 
                WHEN EXISTS (
                    SELECT 1 
                    FROM dbo.ChecklistDetail cd
                    INNER JOIN dbo.PreOperationalChecklist poc ON cd.PreOperationalChecklistId = poc.Id
                    WHERE cd.ChecklistResultId = cr.Id 
                    AND poc.Critical = 1 
                    AND cd.Answer != poc.ExpectedAnswer
                ) THEN 1
                ELSE 0
            END AS HasCriticalQuestions
            FROM dbo.ChecklistResult AS cr
            LEFT JOIN dbo.Session sess ON cr.SessionId1 = sess.Id
            LEFT JOIN dbo.Vehicle v ON v.Id = sess.VehicleId
            LEFT JOIN dbo.Site s ON s.Id = v.SiteId
            LEFT JOIN dbo.Department d ON d.Id = v.DepartmentId
            LEFT JOIN dbo.Customer c ON c.Id = s.CustomerId
			LEFT JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
        WHERE cr.Id = @ChecklistResultId;
    END
    ELSE
    BEGIN
        IF @ReturnTotalCount = 1
        BEGIN
			;WITH VehicleHireHistory AS (
				SELECT 
					vh.VehicleId,
					vh.DepartmentId,
					d.SiteId,
					s.CustomerId,
					vh.HireTime,
					vh.DehireTime
				FROM dbo.VehicleHireDehireHistory vh
				JOIN dbo.Department d ON d.Id = vh.DepartmentId
				JOIN dbo.Site s ON s.Id = d.SiteId
				JOIN dbo.Customer c ON c.Id = s.CustomerId
				WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
				AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate)
			)
            -- Return total count of relevant checklist result
            SELECT COUNT(1) AS TotalRowCount
            FROM dbo.ChecklistResult AS cr
            LEFT JOIN dbo.Session sess ON cr.SessionId1 = sess.Id
            LEFT JOIN dbo.Vehicle v ON v.Id = sess.VehicleId
            LEFT JOIN dbo.Driver dr ON sess.DriverId = dr.Id
            LEFT JOIN dbo.Person p ON dr.Id = p.DriverId
			LEFT JOIN VehicleHireHistory vh ON vh.VehicleId = v.Id
			LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
			LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
			LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
			LEFT JOIN dbo.Dealer de ON de.Id = c.DealerId
			LEFT JOIN dbo.Timezone tz ON tz.Id = s.TimezoneId
            WHERE DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), cr.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
			-- WHERE cr.StartTime BETWEEN @ActualStartDate AND @ActualEndDate
            AND (@CustomerId IS NULL OR c.Id = @CustomerId)
            AND (@SiteId IS NULL OR s.Id = @SiteId)
            AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
            AND 
            (
                @ResultType IS NULL OR 
                (@ResultType = 0 AND cr.EndTime IS NOT NULL AND EXISTS (
                    SELECT 1 FROM dbo.ChecklistDetail cd
                    WHERE cd.ChecklistResultId = cr.Id AND cd.Failed = 1
                )) OR
                (@ResultType = 1 AND cr.EndTime IS NOT NULL AND EXISTS (
                    SELECT 1 FROM dbo.ChecklistDetail cd
                    INNER JOIN dbo.PreOperationalChecklist poc ON cd.PreOperationalChecklistId = poc.Id
                    WHERE cd.ChecklistResultId = cr.Id AND poc.Critical = 1 AND cd.Answer != poc.ExpectedAnswer
                )) OR
                (@ResultType = 2)
            )
            AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' 
                OR d.Name LIKE '%' + @MultiSearch + '%' OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%');
        END
        ELSE
        BEGIN
            -- Calculate the offset for pagination
            DECLARE @RowOffset INT = @PageIndex * @PageSize;
			;WITH VehicleHireHistory AS (
				SELECT 
					vh.VehicleId,
					vh.DepartmentId,
					d.SiteId,
					s.CustomerId,
					vh.HireTime,
					vh.DehireTime
				FROM dbo.VehicleHireDehireHistory vh
				JOIN dbo.Department d ON d.Id = vh.DepartmentId
				JOIN dbo.Site s ON s.Id = d.SiteId
				JOIN dbo.Customer c ON c.Id = s.CustomerId
				WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
				AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate)
			)
            -- Return paginated list of checklist results
            SELECT
                NEWID() AS Id,
                cr.Id AS ChecklistResultId,
				de.Id AS DealerId,
                CASE 
                    WHEN cr.EndTime IS NULL OR cr.StartTime = cr.EndTime THEN 0 
                    ELSE 1
                END AS CheckComplete,
                CASE 
                    WHEN cr.EndTime IS NULL OR cr.StartTime = cr.EndTime THEN '00:00:00'
                    ELSE CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, DATEDIFF(SECOND, cr.StartTime, cr.EndTime), 0), 'HH:mm:ss'))
                END AS CompletionTime,
				DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), cr.StartTime) AS TimezoneAdjustedChecklistStartTime,
                CASE 
                    WHEN EXISTS (
                        SELECT 1 
                        FROM dbo.ChecklistDetail cd
                        INNER JOIN dbo.PreOperationalChecklist poc ON cd.PreOperationalChecklistId = poc.Id
                        WHERE cd.ChecklistResultId = cr.Id 
                        AND poc.Critical = 1 
                        AND cd.Answer != poc.ExpectedAnswer
                    ) THEN 1
                    ELSE 0
                END AS HasCriticalQuestions
            FROM dbo.ChecklistResult AS cr
            LEFT JOIN dbo.Session sess ON cr.SessionId1 = sess.Id
            LEFT JOIN dbo.Vehicle v ON v.Id = sess.VehicleId
            LEFT JOIN dbo.Driver dr ON sess.DriverId = dr.Id
            LEFT JOIN dbo.Person p ON dr.Id = p.DriverId
			LEFT JOIN VehicleHireHistory vh ON vh.VehicleId = v.Id
			LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
			LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
			LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
			LEFT JOIN dbo.Dealer de ON de.Id = c.DealerId
			LEFT JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
            WHERE DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), cr.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
			-- WHERE cr.StartTime BETWEEN @ActualStartDate AND @ActualEndDate
            AND (@CustomerId IS NULL OR c.Id = @CustomerId)
            AND (@SiteId IS NULL OR s.Id = @SiteId)
            AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
            AND 
            (
                @ResultType IS NULL OR 
                (@ResultType = 0 AND cr.EndTime IS NOT NULL AND EXISTS (
                    SELECT 1 FROM dbo.ChecklistDetail cd
                    WHERE cd.ChecklistResultId = cr.Id AND cd.Failed = 1
                )) OR
                (@ResultType = 1 AND EXISTS (
                    SELECT 1 FROM dbo.ChecklistDetail cd
                    INNER JOIN dbo.PreOperationalChecklist poc ON cd.PreOperationalChecklistId = poc.Id
                    WHERE cd.ChecklistResultId = cr.Id AND poc.Critical = 1 AND cd.Answer != poc.ExpectedAnswer
                )) OR
                (@ResultType = 2)
            )
            AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' 
                OR d.Name LIKE '%' + @MultiSearch + '%' OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%')
            ORDER BY cr.EndTime DESC
            OFFSET @RowOffset ROWS FETCH NEXT @PageSize ROWS ONLY;
        END
    END
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetAllVehicleUnlocks]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0,
    @LockoutType INT = NULL,  -- New filter for LockoutType
    @VehicleLockoutId UNIQUEIDENTIFIER = NULL,  -- New filter for VehicleLockoutId
    @MultiSearch NVARCHAR(4000) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    -- Create a temporary table to replace the CTE
    CREATE TABLE #VehicleHireHistory (
        VehicleId UNIQUEIDENTIFIER,
        DepartmentId UNIQUEIDENTIFIER,
        SiteId UNIQUEIDENTIFIER,
        CustomerId UNIQUEIDENTIFIER,
        HireTime DATETIME,
        DehireTime DATETIME
    );

    -- Insert data into the temporary table
    INSERT INTO #VehicleHireHistory
    SELECT 
        vh.VehicleId,
        vh.DepartmentId,
        d.SiteId,
        s.CustomerId,
        vh.HireTime,
        vh.DehireTime
    FROM dbo.VehicleHireDehireHistory vh
    JOIN dbo.Department d ON d.Id = vh.DepartmentId
    JOIN dbo.Site s ON s.Id = d.SiteId
    JOIN dbo.Customer c ON c.Id = s.CustomerId
    WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
    AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate);

    IF @VehicleLockoutId IS NOT NULL
    BEGIN
        -- Return the row corresponding to the provided VehicleLockoutId
        SELECT 
            vl.Id AS Id,
            de.Id AS DealerId,
            vl.Id AS VehicleLockoutId,
            DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), vl.LockoutTime) AS TimezoneAdjustedLockoutDatetime,
            DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), vl.UnlockDateTime) AS TimezoneAdjustedUnlockDatetime,
            CASE 
                WHEN vl.DriverId IS NOT NULL THEN p.FirstName + ' ' + p.LastName 
                ELSE gu.FullName 
            END AS UnlockedBy
        FROM dbo.VehicleLockout vl
        LEFT JOIN dbo.Session sess ON sess.Id = vl.SessionId
        LEFT JOIN dbo.Vehicle v ON sess.VehicleId = v.Id
            LEFT JOIN dbo.Driver dr ON vl.DriverId = dr.Id
            LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
            LEFT JOIN GOSecurity.GOUser gu ON gu.Id = vl.GOUserId
        LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
        LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
        LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
        LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
        LEFT JOIN dbo.Dealer de ON de.Id = c.DealerId
        LEFT JOIN dbo.Timezone tz ON tz.Id = s.TimezoneId
        WHERE vl.Id = @VehicleLockoutId;
    END
    ELSE
    BEGIN
        IF @ReturnTotalCount = 1
        BEGIN
            -- Return total count of vehicle unlocks based on the filters
            SELECT COUNT(1) AS TotalRowCount
            FROM dbo.VehicleLockout vl
            LEFT JOIN dbo.Session sess ON sess.Id = vl.SessionId
            LEFT JOIN dbo.Vehicle v ON sess.VehicleId = v.Id
            LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
            LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
            LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
            LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
            LEFT JOIN dbo.Dealer de ON de.Id = c.DealerId
            LEFT JOIN dbo.Timezone tz ON tz.Id = s.TimezoneId
            WHERE DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), vl.LockoutTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
            -- WHERE vl.LockoutTime BETWEEN @ActualStartDate AND @ActualEndDate
            AND (@CustomerId IS NULL OR c.Id = @CustomerId)
            AND (@SiteId IS NULL OR s.Id = @SiteId)
            AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
            AND (@LockoutType IS NULL OR vl.Reason = @LockoutType)  -- Filter by LockoutType
            AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' OR d.Name LIKE '%' + @MultiSearch + '%');
        END
        ELSE
        BEGIN
            DECLARE @RowOffset INT = @PageIndex * @PageSize;
            SELECT VehicleLockoutId AS Id, 
                DealerId,
                VehicleLockoutId,
                TimezoneAdjustedLockoutDatetime,
                TimezoneAdjustedUnlockDatetime,
                UnlockedBy
            FROM
            (
                SELECT ROW_NUMBER() OVER (ORDER BY LockoutTime DESC) AS RowNum,
                    vl.Id AS VehicleLockoutId,
                    DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), vl.LockoutTime) AS TimezoneAdjustedLockoutDatetime,
                    DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), vl.UnlockDateTime) AS TimezoneAdjustedUnlockDatetime,
                    de.Id as DealerId,
                    CASE 
                        WHEN vl.DriverId IS NOT NULL THEN p.FirstName + ' ' + p.LastName 
                        ELSE gu.FullName 
                    END AS UnlockedBy
                FROM dbo.VehicleLockout vl
                LEFT JOIN dbo.Session sess on sess.id = vl.SessionId
                LEFT JOIN dbo.Vehicle v ON sess.VehicleId = v.Id
                LEFT JOIN dbo.Driver dr ON vl.DriverId = dr.Id
                LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
                LEFT JOIN GOSecurity.GOUser gu ON gu.Id = vl.GOUserId
                LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
                LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
                LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
                LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
                LEFT JOIN dbo.Dealer de ON de.Id = c.DealerId
                LEFT JOIN dbo.Timezone tz ON tz.Id = s.TimezoneId
                WHERE DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), vl.LockoutTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
                -- WHERE vl.LockoutTime BETWEEN @ActualStartDate AND @ActualEndDate
                AND (@CustomerId IS NULL OR c.Id = @CustomerId)
                AND (@SiteId IS NULL OR s.Id = @SiteId)
                AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
                AND (@LockoutType IS NULL OR vl.Reason = @LockoutType)  -- Filter by LockoutType
                AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' OR d.Name LIKE '%' + @MultiSearch + '%')
            ) AS VehicleUnlocks
            WHERE RowNum BETWEEN @RowOffset + 1 AND @RowOffset + @PageSize;
        END
    END
    -- Drop the temporary table to clean up
    DROP TABLE #VehicleHireHistory;
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetDriverProficiency]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0,
    @MultiSearch NVARCHAR(4000) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());
    DECLARE @RowOffset INT = @PageIndex * @PageSize;

    IF @ReturnTotalCount = 1
    BEGIN
        SELECT COUNT(DISTINCT dr.Id) AS TotalRowCount
        FROM dbo.Session AS sess
        LEFT JOIN dbo.Impact AS imp ON sess.Id = imp.SessionId
        LEFT JOIN dbo.Driver dr ON sess.DriverId = dr.Id
        LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
        LEFT JOIN dbo.Department d ON p.DepartmentId = d.Id
        LEFT JOIN dbo.Site s ON d.SiteId = s.Id
        LEFT JOIN dbo.Customer c ON s.CustomerId = c.Id
		LEFT JOIN dbo.Timezone tz ON tz.Id = s.TimezoneId
        WHERE DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), sess.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
        AND (@CustomerId IS NULL OR c.Id = @CustomerId)
        AND (@SiteId IS NULL OR s.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
        AND (@MultiSearch IS NULL OR s.Name LIKE '%' + @MultiSearch + '%' OR d.Name LIKE '%' + @MultiSearch + '%' OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%');
    END
    ELSE
    BEGIN
        ;WITH ImpactCounts AS (
            SELECT
                sess.DriverId,
                COUNT(CASE WHEN imp.ShockValue > imp.Threshold AND imp.ShockValue < (5 * imp.Threshold) THEN 1 END) AS NoOfBlueImpacts,
                COUNT(CASE WHEN imp.ShockValue >= (5 * imp.Threshold) AND imp.ShockValue < (10 * imp.Threshold) THEN 1 END) AS NoOfAmberImpacts,
                COUNT(CASE WHEN imp.ShockValue >= (10 * imp.Threshold) THEN 1 END) AS NoOfRedImpacts
            FROM dbo.Session AS sess
            LEFT JOIN dbo.Impact AS imp ON sess.Id = imp.SessionId
			LEFT JOIN dbo.Driver dr ON sess.DriverId = dr.Id
			LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
			LEFT JOIN dbo.Department d ON p.DepartmentId = d.Id
			LEFT JOIN dbo.Site s ON d.SiteId = s.Id
			LEFT JOIN dbo.Customer c ON s.CustomerId = c.Id
			LEFT JOIN dbo.Timezone tz ON tz.Id = s.TimezoneId
			WHERE DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), sess.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
            AND imp.Threshold != 0
            AND (@CustomerId IS NULL OR c.Id = @CustomerId)
            AND (@SiteId IS NULL OR s.Id = @SiteId)
            AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
            AND (@MultiSearch IS NULL OR s.Name LIKE '%' + @MultiSearch + '%' OR d.Name LIKE '%' + @MultiSearch + '%' OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%')
            GROUP BY sess.DriverId
        ),
        Calculations AS (
            SELECT
                sess.DriverId,
                SUM(CAST(sd.Usage AS DECIMAL(10, 2))) AS TotalDuration
            FROM dbo.Session AS sess
            INNER JOIN dbo.SessionDetails AS sd ON sess.Id = sd.SessionId
            INNER JOIN dbo.IOFIELD AS io ON io.Id = sd.IOFIELDId
			LEFT JOIN dbo.Vehicle v ON sess.VehicleId = v.Id
			LEFT JOIN dbo.Driver dr ON sess.DriverId = dr.Id
			LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
			LEFT JOIN dbo.Site s ON v.SiteId = s.Id
			LEFT JOIN dbo.Department d ON v.DepartmentId = d.Id
			LEFT JOIN dbo.Customer c ON v.CustomerId = c.Id
			LEFT JOIN dbo.Timezone tz ON tz.Id = s.TimezoneId
            WHERE sess.DriverId IS NOT NULL
                AND io.Name = '0'
                AND  DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), sess.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
				AND (@CustomerId IS NULL OR s.CustomerId = @CustomerId)
				AND (@SiteId IS NULL OR s.Id = @SiteId)
				AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
                AND (@MultiSearch IS NULL OR s.Name LIKE '%' + @MultiSearch + '%' OR d.Name LIKE '%' + @MultiSearch + '%' OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%')
            GROUP BY sess.DriverId
        )
        SELECT 
            NEWID() AS Id,
			de.Id as DealerId,
            CONVERT(UNIQUEIDENTIFIER, 'B337F798-B08E-4159-9B45-5D25195F7FCB') AS ProficiencyCombinedViewId,
            sess.DriverId,
			TotalDuration,  -- Check the raw value of TotalDuration
			ISNULL(TotalDuration, -1) AS CheckedTotalDuration,  -- Check if TotalDuration is NULL
			COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TotalDuration, 0), 0), 'HH:mm:ss')), '00:00:00') AS Duration,
            COUNT(DISTINCT sess.Id) AS NoOfSession,
            ISNULL(ic.NoOfBlueImpacts, 0) AS NoOfBlueImpacts,
            ISNULL(ic.NoOfAmberImpacts, 0) AS NoOfAmberImpacts,
            ISNULL(ic.NoOfRedImpacts, 0) AS NoOfRedImpacts,
            ISNULL(CAST(ic.NoOfBlueImpacts AS DECIMAL(10, 2)) / NULLIF(COUNT(DISTINCT sess.Id), 0), 0) AS BlueImpactsPerSession,
            ISNULL(CAST(ic.NoOfAmberImpacts AS DECIMAL(10, 2)) / NULLIF(COUNT(DISTINCT sess.Id), 0), 0) AS AmberImpactsPerSession,
            ISNULL(CAST(ic.NoOfRedImpacts AS DECIMAL(10, 2)) / NULLIF(COUNT(DISTINCT sess.Id), 0), 0) AS RedImpactsPerSession
        FROM dbo.Session AS sess
        LEFT JOIN ImpactCounts AS ic ON sess.DriverId = ic.DriverId
        LEFT JOIN Calculations AS calc ON sess.DriverId = calc.DriverId
        LEFT JOIN dbo.Driver dr ON sess.DriverId = dr.Id
        LEFT JOIN dbo.Person p ON p.DriverId = dr.Id
        LEFT JOIN dbo.Department d ON p.DepartmentId = d.Id
        LEFT JOIN dbo.Site s ON d.SiteId = s.Id
        LEFT JOIN dbo.Customer c ON s.CustomerId = c.Id
		LEFT JOIN dbo.Dealer de ON de.Id = c.DealerId
		LEFT JOIN dbo.Timezone tz ON tz.Id = s.TimezoneId
        WHERE DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), sess.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
        AND (@CustomerId IS NULL OR c.Id = @CustomerId)
        AND (@SiteId IS NULL OR s.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
        AND (@MultiSearch IS NULL OR s.Name LIKE '%' + @MultiSearch + '%' OR d.Name LIKE '%' + @MultiSearch + '%' OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%')
        GROUP BY sess.DriverId, de.Id, ic.NoOfBlueImpacts, ic.NoOfAmberImpacts, ic.NoOfRedImpacts, calc.TotalDuration
        ORDER BY sess.DriverId DESC
        OFFSET @RowOffset ROWS FETCH NEXT @PageSize ROWS ONLY;
    END
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetVehicleProficiency]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0,
    @MultiSearch NVARCHAR(4000) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());
    DECLARE @RowOffset INT = @PageIndex * @PageSize;

    -- Create a temporary table to replace the CTE
    CREATE TABLE #VehicleHireHistory (
        VehicleId UNIQUEIDENTIFIER,
        DepartmentId UNIQUEIDENTIFIER,
        SiteId UNIQUEIDENTIFIER,
        CustomerId UNIQUEIDENTIFIER,
        HireTime DATETIME,
        DehireTime DATETIME
    );

    -- Insert data into the temporary table
    INSERT INTO #VehicleHireHistory
    SELECT 
        vh.VehicleId,
        vh.DepartmentId,
        d.SiteId,
        s.CustomerId,
        vh.HireTime,
        vh.DehireTime
    FROM dbo.VehicleHireDehireHistory vh
    JOIN dbo.Department d ON d.Id = vh.DepartmentId
    JOIN dbo.Site s ON s.Id = d.SiteId
    JOIN dbo.Customer c ON c.Id = s.CustomerId
    WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
    AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate);

    IF @ReturnTotalCount = 1
    BEGIN
        SELECT COUNT(DISTINCT v.Id) AS TotalRowCount
        FROM dbo.Session AS sess
        LEFT JOIN dbo.Impact AS imp ON sess.Id = imp.SessionId
		LEFT JOIN dbo.Vehicle v ON sess.VehicleId = v.Id
        LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
        LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
        LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
        LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
		LEFT JOIN dbo.Timezone tz ON tz.Id = s.TimezoneId
        WHERE DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), sess.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
        AND (@CustomerId IS NULL OR s.CustomerId = @CustomerId)
        AND (@SiteId IS NULL OR s.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
        AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' OR d.Name LIKE '%' + @MultiSearch + '%');
    END
    ELSE
    BEGIN
        ;WITH ImpactCounts AS (
            SELECT
                sess.VehicleId,
                COUNT(CASE WHEN imp.ShockValue > imp.Threshold AND imp.ShockValue < (5 * imp.Threshold) THEN 1 END) AS NoOfBlueImpacts,
                COUNT(CASE WHEN imp.ShockValue >= (5 * imp.Threshold) AND imp.ShockValue < (10 * imp.Threshold) THEN 1 END) AS NoOfAmberImpacts,
                COUNT(CASE WHEN imp.ShockValue >= (10 * imp.Threshold) THEN 1 END) AS NoOfRedImpacts
            FROM dbo.Session AS sess
            LEFT JOIN dbo.Impact AS imp ON sess.Id = imp.SessionId
			LEFT JOIN dbo.Vehicle v ON sess.VehicleId = v.Id
            LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
            LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
            LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
            LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
			LEFT JOIN dbo.Timezone tz ON tz.Id = s.TimezoneId
            WHERE DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), sess.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
            AND imp.Threshold != 0
            AND (@CustomerId IS NULL OR s.CustomerId = @CustomerId)
            AND (@SiteId IS NULL OR s.Id = @SiteId)
            AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
            AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' OR d.Name LIKE '%' + @MultiSearch + '%')
            GROUP BY sess.VehicleId
        ),
        Calculations AS (
            SELECT
                sess.VehicleId,
                SUM(CAST(sd.Usage AS DECIMAL(10, 2))) AS TotalDuration
            FROM dbo.Session AS sess
            INNER JOIN dbo.SessionDetails AS sd ON sess.Id = sd.SessionId
            INNER JOIN dbo.IOFIELD AS io ON io.Id = sd.IOFIELDId
			LEFT JOIN dbo.Vehicle v ON sess.VehicleId = v.Id
            LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
            LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
            LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
            LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
			LEFT JOIN dbo.Timezone tz ON tz.Id = s.TimezoneId
            WHERE sess.VehicleId IS NOT NULL
                AND io.Name = '0'
                AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), sess.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
				AND (@CustomerId IS NULL OR s.CustomerId = @CustomerId)
				AND (@SiteId IS NULL OR s.Id = @SiteId)
				AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
                AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' OR d.Name LIKE '%' + @MultiSearch + '%')
            GROUP BY sess.VehicleId
        )
        SELECT 
			NEWID() AS Id,
            CONVERT(UNIQUEIDENTIFIER, 'B337F798-B08E-4159-9B45-5D25195F7FCB') AS ProficiencyCombinedViewId,
            sess.VehicleId,
			de.Id AS DealerId,
			TotalDuration,  -- Check the raw value of TotalDuration
			ISNULL(TotalDuration, -1) AS CheckedTotalDuration,  -- Check if TotalDuration is NULL
			COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TotalDuration, 0), 0), 'HH:mm:ss')), '00:00:00') AS Duration,
            COUNT(DISTINCT sess.Id) AS NoOfSession,
            ISNULL(CAST(ic.NoOfBlueImpacts AS INT), 0) AS NoOfBlueImpacts,
            ISNULL(CAST(ic.NoOfAmberImpacts AS INT), 0) AS NoOfAmberImpacts,
            ISNULL(CAST(ic.NoOfRedImpacts AS INT), 0) AS NoOfRedImpacts,
            ISNULL(CAST(CAST(ic.NoOfBlueImpacts AS DECIMAL(10, 2)) / NULLIF(COUNT(DISTINCT s.Id), 0) AS DECIMAL (10, 2)), 0) AS BlueImpactsPerSession,
            ISNULL(CAST(CAST(ic.NoOfAmberImpacts AS DECIMAL(10, 2)) / NULLIF(COUNT(DISTINCT s.Id), 0) AS DECIMAL (10, 2)), 0) AS AmberImpactsPerSession,
            ISNULL(CAST(CAST(ic.NoOfRedImpacts AS DECIMAL(10, 2)) / NULLIF(COUNT(DISTINCT s.Id), 0) AS DECIMAL (10, 2)), 0) AS RedImpactsPerSession
        FROM dbo.Session AS sess
        LEFT JOIN ImpactCounts AS ic ON sess.VehicleId = ic.VehicleId
        LEFT JOIN Calculations AS calc ON sess.VehicleId = calc.VehicleId
		LEFT JOIN dbo.Vehicle v ON sess.VehicleId = v.Id
        LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
        LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
        LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
        LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
		LEFT JOIN dbo.Dealer de ON de.Id = c.DealerId
		LEFT JOIN dbo.Timezone tz ON tz.Id = s.TimezoneId
        WHERE DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), sess.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
        AND (@CustomerId IS NULL OR s.CustomerId = @CustomerId)
        AND (@SiteId IS NULL OR s.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
        AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' OR d.Name LIKE '%' + @MultiSearch + '%')
        GROUP BY sess.VehicleId, de.Id, ic.NoOfBlueImpacts, ic.NoOfAmberImpacts, ic.NoOfRedImpacts, calc.TotalDuration
        ORDER BY sess.VehicleId DESC
        OFFSET @RowOffset ROWS FETCH NEXT @PageSize ROWS ONLY;
    END
    -- Drop the temporary table to clean up
    DROP TABLE #VehicleHireHistory;
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetDriverLicenseDetails]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @MultiSearch NVARCHAR(4000) = NULL,
    @LicenseType INT = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    IF @ReturnTotalCount = 1
    BEGIN
        SELECT COUNT(1) AS TotalRowCount
        FROM (
            -- Count from LicenseDetail
            SELECT
                ld.Id AS LicenseId
            FROM dbo.Person p
            LEFT JOIN dbo.Customer c ON p.CustomerId = c.Id
            LEFT JOIN dbo.Site site ON p.SiteId = site.Id
            LEFT JOIN dbo.Department dept ON p.DepartmentId = dept.Id
            LEFT JOIN dbo.Driver dr ON p.DriverId = dr.Id
            LEFT JOIN dbo.LicenceDetail ld ON dr.LicenceDetailId = ld.Id
            WHERE
                ld.Id IS NOT NULL AND
                (@LicenseType IS NULL OR @LicenseType = 0) AND
                (@CustomerId IS NULL OR c.Id = @CustomerId) AND
                (@SiteId IS NULL OR p.SiteId = @SiteId) AND
                (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId) AND
                (@MultiSearch IS NULL OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%' OR site.Name LIKE '%' + @MultiSearch + '%' OR dept.Name LIKE '%' + @MultiSearch + '%')

            UNION ALL

            -- Count from LicenseByModel
            SELECT
                lbm.Id AS LicenseId
            FROM dbo.Person p
            LEFT JOIN dbo.Customer c ON p.CustomerId = c.Id
            LEFT JOIN dbo.Site site ON p.SiteId = site.Id
            LEFT JOIN dbo.Department dept ON p.DepartmentId = dept.Id
            LEFT JOIN dbo.Driver dr ON p.DriverId = dr.Id
            LEFT JOIN dbo.LicenseByModel lbm ON dr.Id = lbm.DriverId
            LEFT JOIN dbo.Model m ON lbm.ModelId = m.Id
            WHERE
                lbm.Id IS NOT NULL AND
                (@LicenseType IS NULL OR @LicenseType = 1) AND
                (@CustomerId IS NULL OR c.Id = @CustomerId) AND
                (@SiteId IS NULL OR p.SiteId = @SiteId) AND
                (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId) AND
                (@MultiSearch IS NULL OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%' OR site.Name LIKE '%' + @MultiSearch + '%' OR dept.Name LIKE '%' + @MultiSearch + '%')
        ) AS CombinedQuery;
    END
    ELSE
    BEGIN
        DECLARE @RowOffset INT = @PageIndex * @PageSize;

        SELECT
            NEWID() AS Id,
            q.DriverId,
            q.DealerId,
            q.ExpiryDate,
            q.LicenseType,
            q.Model,
            CASE 
                WHEN q.ExpiryDate < GETDATE() THEN 0  -- Expired
                WHEN q.ExpiryDate < DATEADD(DAY, 7, GETDATE()) THEN 1  -- Expiring in 1 Week
                WHEN q.ExpiryDate < DATEADD(MONTH, 1, GETDATE()) THEN 2  -- Expiring in 1 Month
                WHEN q.ExpiryDate < DATEADD(MONTH, 3, GETDATE()) THEN 3  -- Expiring in 3 Months
                WHEN q.ExpiryDate < DATEADD(MONTH, 6, GETDATE()) THEN 4  -- Expiring in 6 Months
                ELSE 5  -- Valid
            END AS Status
        FROM (
            -- Query from LicenseDetail
            SELECT
                p.DriverId,
                c.DealerId,
                ld.ExpiryDate,
                'General' AS LicenseType,
                'N/A' AS Model
            FROM dbo.Person p
            LEFT JOIN dbo.Customer c ON p.CustomerId = c.Id
            LEFT JOIN dbo.Site site ON p.SiteId = site.Id
            LEFT JOIN dbo.Department dept ON p.DepartmentId = dept.Id
            LEFT JOIN dbo.Driver dr ON p.DriverId = dr.Id
            LEFT JOIN dbo.LicenceDetail ld ON dr.LicenceDetailId = ld.Id
            WHERE
                ld.Id IS NOT NULL AND
                (@LicenseType IS NULL OR @LicenseType = 0) AND
                (@CustomerId IS NULL OR c.Id = @CustomerId) AND
                (@SiteId IS NULL OR p.SiteId = @SiteId) AND
                (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId) AND
                (@MultiSearch IS NULL OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%' OR site.Name LIKE '%' + @MultiSearch + '%' OR dept.Name LIKE '%' + @MultiSearch + '%')

            UNION ALL

            -- Query from LicenseByModel
            SELECT
                p.DriverId,
                c.DealerId,
                lbm.ExpiryDate,
                'By Model' AS LicenseType,
                m.Name AS Model
            FROM dbo.Person p
            LEFT JOIN dbo.Customer c ON p.CustomerId = c.Id
            LEFT JOIN dbo.Site site ON p.SiteId = site.Id
            LEFT JOIN dbo.Department dept ON p.DepartmentId = dept.Id
            LEFT JOIN dbo.Driver dr ON p.DriverId = dr.Id
            LEFT JOIN dbo.LicenseByModel lbm ON dr.Id = lbm.DriverId
            LEFT JOIN dbo.Model m ON lbm.ModelId = m.Id
            WHERE
                lbm.Id IS NOT NULL AND
                (@LicenseType IS NULL OR @LicenseType = 1) AND
                (@CustomerId IS NULL OR c.Id = @CustomerId) AND
                (@SiteId IS NULL OR p.SiteId = @SiteId) AND
                (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId) AND
                (@MultiSearch IS NULL OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%' OR site.Name LIKE '%' + @MultiSearch + '%' OR dept.Name LIKE '%' + @MultiSearch + '%')
        ) q
        ORDER BY q.ExpiryDate DESC
        OFFSET @RowOffset ROWS
        FETCH NEXT @PageSize ROWS ONLY;
    END
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetDriverAccessAbuseReport]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0,
    @MultiSearch NVARCHAR(4000) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    IF @ReturnTotalCount = 1
    BEGIN
        WITH SessionOverlap AS (
            SELECT 
                s1.DriverId,
                c.DealerId,
                p.CustomerId,
                p.SiteId,
                p.DepartmentId,
                SUM(
                    CASE 
                        WHEN s2.StartTime < s1.EndTime AND s1.StartTime < s2.EndTime 
                        THEN DATEDIFF(SECOND, 
                            CASE WHEN s2.StartTime > s1.StartTime THEN s2.StartTime ELSE s1.StartTime END,
                            CASE WHEN s1.EndTime < s2.EndTime THEN s1.EndTime ELSE s2.EndTime END
                        )
                        ELSE 0 
                    END
                ) AS OverlapTimeSeconds
            FROM dbo.Session s1
            INNER JOIN dbo.Session s2 
                ON s1.DriverId = s2.DriverId 
                AND s1.Id <> s2.Id
                AND s1.StartTime < s2.StartTime  -- This condition ensures we only count the overlap once
            INNER JOIN dbo.Driver dr ON s1.DriverId = dr.Id
            INNER JOIN dbo.Person p ON dr.Id = p.DriverId
            INNER JOIN dbo.Customer c ON p.CustomerId = c.Id
            LEFT JOIN dbo.Vehicle v ON s1.VehicleId = v.Id
            LEFT JOIN dbo.Site site ON p.SiteId = site.Id
            LEFT JOIN dbo.Department de ON p.DepartmentId = de.Id
            LEFT JOIN dbo.Timezone tz ON site.TimezoneId = tz.Id
            WHERE 
                s1.DriverId IS NOT NULL
                AND s2.StartTime < s1.EndTime
                AND s1.StartTime < s2.EndTime
                AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s1.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
                AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s2.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
                AND (@CustomerId IS NULL OR p.CustomerId = @CustomerId)
                AND (@SiteId IS NULL OR p.SiteId = @SiteId)
                AND (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId)
                AND (@MultiSearch IS NULL OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%' 
                    OR site.Name LIKE '%' + @MultiSearch + '%' 
                    OR de.Name LIKE '%' + @MultiSearch + '%')
            GROUP BY s1.DriverId, c.DealerId, p.CustomerId, p.SiteId, p.DepartmentId
            HAVING SUM(
                CASE 
                    WHEN s2.StartTime < s1.EndTime AND s1.StartTime < s2.EndTime 
                    THEN DATEDIFF(SECOND, 
                        CASE WHEN s2.StartTime > s1.StartTime THEN s2.StartTime ELSE s1.StartTime END,
                        CASE WHEN s1.EndTime < s2.EndTime THEN s1.EndTime ELSE s2.EndTime END
                    )
                    ELSE 0 
                END
            ) > 0  -- Ensure only drivers with overlap time are included
        )
        SELECT COUNT(1) AS TotalRowCount
        FROM SessionOverlap;
    END
    ELSE
    BEGIN
        DECLARE @RowOffset INT = @PageIndex * @PageSize;

        WITH SessionOverlap AS (
            SELECT 
                s1.DriverId,
                c.DealerId,
                p.CustomerId,
                p.SiteId,
                p.DepartmentId,
                SUM(
                    CASE 
                        WHEN s2.StartTime < s1.EndTime AND s1.StartTime < s2.EndTime 
                        THEN DATEDIFF(SECOND, 
                            CASE WHEN s2.StartTime > s1.StartTime THEN s2.StartTime ELSE s1.StartTime END,
                            CASE WHEN s1.EndTime < s2.EndTime THEN s1.EndTime ELSE s2.EndTime END
                        )
                        ELSE 0 
                    END
                ) AS OverlapTimeSeconds
            FROM dbo.Session s1
            INNER JOIN dbo.Session s2 
                ON s1.DriverId = s2.DriverId 
                AND s1.Id <> s2.Id
                AND s1.StartTime < s2.StartTime  -- This condition ensures we only count the overlap once
            INNER JOIN dbo.Driver dr ON s1.DriverId = dr.Id
            INNER JOIN dbo.Person p ON dr.Id = p.DriverId
            INNER JOIN dbo.Customer c ON p.CustomerId = c.Id
            LEFT JOIN dbo.Vehicle v ON s1.VehicleId = v.Id
            LEFT JOIN dbo.Site site ON p.SiteId = site.Id
            LEFT JOIN dbo.Department de ON p.DepartmentId = de.Id
            LEFT JOIN dbo.Timezone tz ON site.TimezoneId = tz.Id
            WHERE 
                s1.DriverId IS NOT NULL
                AND s2.StartTime < s1.EndTime
                AND s1.StartTime < s2.EndTime
                AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s1.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
                AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s2.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
                AND (@CustomerId IS NULL OR p.CustomerId = @CustomerId)
                AND (@SiteId IS NULL OR p.SiteId = @SiteId)
                AND (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId)
                AND (@MultiSearch IS NULL OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%' 
                    OR site.Name LIKE '%' + @MultiSearch + '%' 
                    OR de.Name LIKE '%' + @MultiSearch + '%')
            GROUP BY s1.DriverId, c.DealerId, p.CustomerId, p.SiteId, p.DepartmentId
            HAVING SUM(
                CASE 
                    WHEN s2.StartTime < s1.EndTime AND s1.StartTime < s2.EndTime 
                    THEN DATEDIFF(SECOND, 
                        CASE WHEN s2.StartTime > s1.StartTime THEN s2.StartTime ELSE s1.StartTime END,
                        CASE WHEN s1.EndTime < s2.EndTime THEN s1.EndTime ELSE s2.EndTime END
                    )
                    ELSE 0 
                END
            ) > 0  -- Ensure only drivers with overlap time are included
        )
        SELECT 
            NEWID() AS Id,
            SessionOverlap.DriverId,
            SessionOverlap.DealerId,
            CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(SessionOverlap.OverlapTimeSeconds, 0), 0), 'HH:mm:ss')) AS OverlapTime
        FROM SessionOverlap
        ORDER BY SessionOverlap.DriverId
        OFFSET @RowOffset ROWS FETCH NEXT @PageSize ROWS ONLY;
    END
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetVehicleCalibrationReport]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @MultiSearch NVARCHAR(4000) = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    -- Declare the total row count variable
    DECLARE @TotalRowCount INT;

    -- Calculate the row offset
    DECLARE @RowOffset INT = @PageIndex * @PageSize;

    -- If ReturnTotalCount is 1, calculate the total number of rows
    IF @ReturnTotalCount = 1
    BEGIN
        SELECT @TotalRowCount = COUNT(1)
        FROM dbo.Vehicle v
        INNER JOIN dbo.Module m ON m.Id = v.ModuleId1
        LEFT JOIN dbo.Customer c ON v.CustomerId = c.Id
        LEFT JOIN dbo.Dealer de ON c.DealerId = de.Id
        LEFT JOIN dbo.Site s ON v.SiteId = s.Id
        LEFT JOIN dbo.Department d ON v.DepartmentId = d.Id
        WHERE
            (@CustomerId IS NULL OR c.Id = @CustomerId)
            AND (@SiteId IS NULL OR v.SiteId = @SiteId)
            AND (@DepartmentId IS NULL OR v.DepartmentId = @DepartmentId)
            AND (@MultiSearch IS NULL OR v.Id LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' 
                OR d.Name LIKE '%' + @MultiSearch + '%');

        -- Return the total row count
        SELECT @TotalRowCount AS TotalRowCount;
        RETURN;
    END
	ELSE
    -- Main query for retrieving the vehicle calibration report
    SELECT
        NEWID() AS Id,                              -- Generate a unique ID for each row
        v.Id AS VehicleId,                          -- Vehicle Id
        de.Id AS DealerId,                          -- Dealer Id
        FORMAT(m.FSSXMulti, '0.00') + '%' AS AdjustCalibrationLevel,   -- Format the FSSXMulti as percentage
        FORMAT(m.Calibration, '0.00') + '%' AS CalibrationLevel,       -- Format the Calibration as percentage
        CASE 
            WHEN m.Calibration = 100 THEN 'Yes'     -- If Calibration is 100%, ImpactCalibrated is "Yes"
            ELSE 'No'
        END AS ImpactCalibrated,
        CASE 
            WHEN v.ImpactLockout = 1 THEN 'Yes'     -- If ImpactLockout is enabled, set to "Yes"
            ELSE 'No'
        END AS ImpactLockoutEnabled
    FROM dbo.Vehicle v
    INNER JOIN dbo.Module m ON m.Id = v.ModuleId1 -- Join vehicle with its module
    LEFT JOIN dbo.Customer c ON v.CustomerId = c.Id
    LEFT JOIN dbo.Dealer de ON c.DealerId = de.Id  -- Join to get DealerId
    LEFT JOIN dbo.Site s ON v.SiteId = s.Id
    LEFT JOIN dbo.Department d ON v.DepartmentId = d.Id
    WHERE
        (@CustomerId IS NULL OR c.Id = @CustomerId)   -- Filter by CustomerId
        AND (@SiteId IS NULL OR v.SiteId = @SiteId)   -- Filter by SiteId
        AND (@DepartmentId IS NULL OR v.DepartmentId = @DepartmentId) -- Filter by DepartmentId
        AND (@MultiSearch IS NULL OR v.Id LIKE '%' + @MultiSearch + '%' OR s.Name LIKE '%' + @MultiSearch + '%' 
            OR d.Name LIKE '%' + @MultiSearch + '%')
    ORDER BY v.Id
    OFFSET @RowOffset ROWS FETCH NEXT @PageSize ROWS ONLY; -- Pagination logic
END
GO

CREATE OR ALTER PROCEDURE [dbo].[GetEmailSubscription]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @MultiSearch NVARCHAR(4000) = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    -- Declare the total row count variable
    DECLARE @TotalRowCount INT;

    -- Calculate the row offset
    DECLARE @RowOffset INT = @PageIndex * @PageSize;

    -- If ReturnTotalCount is 1, calculate the total number of rows
    IF @ReturnTotalCount = 1
    BEGIN
        SELECT @TotalRowCount = COUNT(1)
        FROM dbo.ReportSubscription rs
        INNER JOIN dbo.Person p ON rs.PersonId = p.Id
        INNER JOIN dbo.Customer c ON p.CustomerId = c.Id
        LEFT JOIN dbo.Site s ON p.SiteId = s.Id
        LEFT JOIN dbo.Department d ON p.DepartmentId = d.Id
        WHERE
            (@CustomerId IS NULL OR c.Id = @CustomerId)
            AND (@SiteId IS NULL OR p.SiteId = @SiteId)
            AND (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId)
            AND (@MultiSearch IS NULL OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%'
                OR s.Name LIKE '%' + @MultiSearch + '%' 
                OR d.Name LIKE '%' + @MultiSearch + '%');

        -- Return the total row count
        SELECT @TotalRowCount AS TotalRowCount;
        RETURN;
    END
	ELSE
    -- Main query for retrieving the email subscriptions
    SELECT
        NEWID() AS Id,                              -- Generate a unique ID for each row
        rs.Id AS ReportSubscriptionId,              -- Report Subscription Id
        CASE 
            WHEN rs.Frequency = 0 THEN '1'
            WHEN rs.Frequency = 1 THEN '7'
            WHEN rs.Frequency = 2 THEN '30'
            ELSE 'Unknown'
        END AS FrequencyDays                        -- Calculate FrequencyDays based on the Frequency
    FROM dbo.ReportSubscription rs
    INNER JOIN dbo.Person p ON rs.PersonId = p.Id   -- Join ReportSubscription with Person
    INNER JOIN dbo.Customer c ON p.CustomerId = c.Id
    LEFT JOIN dbo.Site s ON p.SiteId = s.Id
    LEFT JOIN dbo.Department d ON p.DepartmentId = d.Id
    WHERE
        (@CustomerId IS NULL OR c.Id = @CustomerId)   -- Filter by CustomerId
        AND (@SiteId IS NULL OR p.SiteId = @SiteId)   -- Filter by SiteId
        AND (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId) -- Filter by DepartmentId
        AND (@MultiSearch IS NULL OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%' 
            OR s.Name LIKE '%' + @MultiSearch + '%' 
            OR d.Name LIKE '%' + @MultiSearch + '%')
    ORDER BY rs.Id
    OFFSET @RowOffset ROWS FETCH NEXT @PageSize ROWS ONLY; -- Pagination logic
END
GO

CREATE OR ALTER PROCEDURE [dbo].[GetUserSummary]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @MultiSearch NVARCHAR(4000) = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @RowOffset INT = @PageIndex * @PageSize;

    -- If ReturnTotalCount is 1, calculate and return the total number of rows
    IF @ReturnTotalCount = 1
    BEGIN
        SELECT COUNT(1) AS TotalRowCount
        FROM dbo.Person p
        INNER JOIN GOSecurity.GOUser gu ON p.GOUserId = gu.Id  -- Join Person with GOUser to filter by Username
        INNER JOIN dbo.Customer c ON p.CustomerId = c.Id
        LEFT JOIN dbo.Site s ON p.SiteId = s.Id
        LEFT JOIN dbo.Department d ON p.DepartmentId = d.Id
        WHERE gu.Username IS NOT NULL
            AND (@CustomerId IS NULL OR p.CustomerId = @CustomerId)
            AND (@SiteId IS NULL OR p.SiteId = @SiteId)
            AND (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId)
            AND (@MultiSearch IS NULL OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%' 
                OR s.Name LIKE '%' + @MultiSearch + '%' 
                OR d.Name LIKE '%' + @MultiSearch + '%');
        
        RETURN;
    END

    -- Main query for retrieving the email subscriptions
    SELECT
        NEWID() AS Id,                              -- Generate a unique ID for each row
        p.Id AS PersonId,                           -- Person Id
        c.DealerId                                  -- Dealer Id
    FROM dbo.Person p
    INNER JOIN GOSecurity.GOUser gu ON p.GOUserId = gu.Id  -- Join Person with GOUser to filter by Usernameme
    INNER JOIN dbo.Customer c ON p.CustomerId = c.Id
    LEFT JOIN dbo.Site s ON p.SiteId = s.Id
    LEFT JOIN dbo.Department d ON p.DepartmentId = d.Id
    WHERE gu.Username IS NOT NULL
        AND (@CustomerId IS NULL OR p.CustomerId = @CustomerId)   -- Filter by CustomerId
        AND (@SiteId IS NULL OR p.SiteId = @SiteId)               -- Filter by SiteId
        AND (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId) -- Filter by DepartmentId
        AND (@MultiSearch IS NULL OR p.FirstName + ' ' + p.LastName LIKE '%' + @MultiSearch + '%' 
            OR s.Name LIKE '%' + @MultiSearch + '%' 
            OR d.Name LIKE '%' + @MultiSearch + '%')
    ORDER BY p.Id
    OFFSET @RowOffset ROWS FETCH NEXT @PageSize ROWS ONLY; -- Pagination logic
END
GO

CREATE OR ALTER VIEW [dbo].[VORReportCombinedView] AS
SELECT
	'C187F702-B41E-4578-1B57-5D82395F7FCA' AS Id
GO

CREATE OR ALTER PROCEDURE [dbo].[GetVORSessions]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0,
    @MultiSearch NVARCHAR(4000) = NULL,
    @VehicleId UNIQUEIDENTIFIER = NULL -- New parameter for VehicleId
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());
    
    -- Create a temporary table to replace the CTE
    CREATE TABLE #VehicleHireHistory (
        VehicleId UNIQUEIDENTIFIER,
        DepartmentId UNIQUEIDENTIFIER,
        SiteId UNIQUEIDENTIFIER,
        CustomerId UNIQUEIDENTIFIER,
        HireTime DATETIME,
        DehireTime DATETIME
    );

    -- Insert data into the temporary table
    INSERT INTO #VehicleHireHistory
    SELECT 
        vh.VehicleId,
        vh.DepartmentId,
        d.SiteId,
        s.CustomerId,
        vh.HireTime,
        vh.DehireTime
    FROM dbo.VehicleHireDehireHistory vh
    JOIN dbo.Department d ON d.Id = vh.DepartmentId
    JOIN dbo.Site s ON s.Id = d.SiteId
    JOIN dbo.Customer c ON c.Id = s.CustomerId
    WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
    AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate);

    IF @VehicleId IS NOT NULL
    BEGIN
        -- Return the row corresponding to the provided @VehicleId
        WITH Calculations AS (
            SELECT
                s.VehicleId,
                SUM(CAST(sd.Usage AS DECIMAL(10, 2))) AS TotalDuration
            FROM dbo.Session AS s
            INNER JOIN dbo.SessionDetails AS sd ON s.Id = sd.SessionId
            INNER JOIN dbo.IOFIELD AS io ON io.Id = sd.IOFIELDId
            INNER JOIN dbo.Vehicle AS v ON s.VehicleId = v.Id
            LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
            LEFT JOIN dbo.Site st  ON ISNULL(vh.SiteId, v.SiteId) = st.Id
            LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
            LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, st.CustomerId) = c.Id
            INNER JOIN dbo.Timezone tz ON st.TimezoneId = tz.Id
            WHERE s.DriverId IS NOT NULL
                AND io.Name = '0'
                AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
                AND (@CustomerId IS NULL OR c.Id = @CustomerId)
                AND (@SiteId IS NULL OR st.Id = @SiteId)
                AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
                AND s.isVOR = 1  -- Only return records where isVOR is True
            GROUP BY s.VehicleId
        ),
        SeatCalc AS (
            SELECT
                s.VehicleId,
                ROUND(SUM(CAST(sd.Usage AS DECIMAL(10, 2))), 2) AS TotalSeatHours
            FROM dbo.Session AS s
            INNER JOIN dbo.SessionDetails AS sd ON s.Id = sd.SessionId
            INNER JOIN dbo.IOFIELD AS io ON io.Id = sd.IOFIELDId
            INNER JOIN dbo.Vehicle AS v ON s.VehicleId = v.Id
            LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
            LEFT JOIN dbo.Site st  ON ISNULL(vh.SiteId, v.SiteId) = st.Id
            LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
            LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, st.CustomerId) = c.Id
            INNER JOIN dbo.Timezone tz ON st.TimezoneId = tz.Id
            WHERE s.DriverId IS NOT NULL
                AND (io.Name = '4' OR io.Name = 'SEAT')
                AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
                AND (@CustomerId IS NULL OR c.Id = @CustomerId)
                AND (@SiteId IS NULL OR st.Id = @SiteId)
                AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
                AND s.isVOR = 1  -- Only return records where isVOR is True
            GROUP BY s.VehicleId
        ),
        HydraulicCalc AS (
            SELECT
                s.VehicleId,
                ROUND(SUM(CAST(sd.Usage AS DECIMAL(10, 2))), 2) AS TotalHydraulicHours
            FROM dbo.Session AS s
            INNER JOIN dbo.SessionDetails AS sd ON s.Id = sd.SessionId
            INNER JOIN dbo.IOFIELD AS io ON io.Id = sd.IOFIELDId
            INNER JOIN dbo.Vehicle AS v ON s.VehicleId = v.Id
            LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
            LEFT JOIN dbo.Site st ON ISNULL(vh.SiteId, v.SiteId) = st.Id
            LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
            LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, st.CustomerId) = c.Id
            INNER JOIN dbo.Timezone tz ON st.TimezoneId = tz.Id
            WHERE s.DriverId IS NOT NULL
                AND (io.Name = 'HYDR' OR io.Name = 'HYDL' OR io.Name = '1')
                AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
                AND (@CustomerId IS NULL OR c.Id = @CustomerId)
                AND (@SiteId IS NULL OR st.Id = @SiteId)
                AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
                AND s.isVOR = 1  -- Only return records where isVOR is True
            GROUP BY s.VehicleId
        ),
        TractionCalc AS (
            SELECT
                s.VehicleId,
                ROUND(SUM(CAST(sd.Usage AS DECIMAL(10, 2))), 2) AS TotalTractionHours
            FROM dbo.Session AS s
            INNER JOIN dbo.SessionDetails AS sd ON s.Id = sd.SessionId
            INNER JOIN dbo.IOFIELD AS io ON io.Id = sd.IOFIELDId
            INNER JOIN dbo.Vehicle AS v ON s.VehicleId = v.Id
            LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
            LEFT JOIN dbo.Site st ON ISNULL(vh.SiteId, v.SiteId) = st.Id
            LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
            LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, st.CustomerId) = c.Id
            INNER JOIN dbo.Timezone tz ON st.TimezoneId = tz.Id
            WHERE s.DriverId IS NOT NULL
                AND (io.Name = 'TRACK' OR io.Name = '2')
                AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
                AND (@CustomerId IS NULL OR c.Id = @CustomerId)
                AND (@SiteId IS NULL OR st.Id = @SiteId)
                AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
                AND s.isVOR = 1  -- Only return records where isVOR is True
            GROUP BY s.VehicleId
        )
        SELECT
            NEWID() AS Id,
            main.VehicleId,
            -- de.Id AS DealerId,
            CAST('C187F702-B41E-4578-1B57-5D82395F7FCA' AS UNIQUEIDENTIFIER) AS VORReportCombinedViewId,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TotalDuration, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalDuration,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(SeatCalc.TotalSeatHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalSeatHours,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(HydraulicCalc.TotalHydraulicHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalHydraulicHours,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TractionCalc.TotalTractionHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalTractionHours
        FROM dbo.Session AS main
        INNER JOIN dbo.Vehicle AS v ON v.Id = main.VehicleId
        LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
        LEFT JOIN dbo.Site st ON ISNULL(vh.SiteId, v.SiteId) = st.Id
        LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
        LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, st.CustomerId) = c.Id
        INNER JOIN dbo.Dealer de ON c.DealerId = de.Id
        LEFT JOIN Calculations ON main.VehicleId = Calculations.VehicleId
        LEFT JOIN SeatCalc ON main.VehicleId = SeatCalc.VehicleId
        LEFT JOIN HydraulicCalc ON main.VehicleId = HydraulicCalc.VehicleId
        LEFT JOIN TractionCalc ON main.VehicleId = TractionCalc.VehicleId
        WHERE main.VehicleId = @VehicleId 
        AND main.EndTime BETWEEN @ActualStartDate AND @ActualEndDate
        AND (@CustomerId IS NULL OR c.Id = @CustomerId)
        AND (@SiteId IS NULL OR st.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
        AND main.isVOR = 1  -- Only return records where isVOR is True
    END
    ELSE
    BEGIN
        IF @ReturnTotalCount = 1
        BEGIN
            -- Counting the number of distinct vehicle IDs matching the filter criteria
            SELECT COUNT(DISTINCT v.Id) AS TotalRowCount
            FROM dbo.Session AS main
            INNER JOIN dbo.Vehicle AS v ON main.VehicleId = v.Id
            LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
            LEFT JOIN dbo.Site st ON ISNULL(vh.SiteId, v.SiteId) = st.Id
            LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
            LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, st.CustomerId) = c.Id
            INNER JOIN dbo.Timezone tz ON st.TimezoneId = tz.Id
            WHERE main.DriverId IS NOT NULL
            AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), main.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
            AND (@CustomerId IS NULL OR c.Id = @CustomerId)
            AND (@SiteId IS NULL OR st.Id = @SiteId)
            AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
            AND main.isVOR = 1  -- Only return records where isVOR is True
        END
        ELSE
        BEGIN
            -- Retrieving paginated data with filters applied
            DECLARE @RowOffset INT = @PageIndex * @PageSize;

            WITH Calculations AS (
                SELECT
                    s.VehicleId,
                    SUM(CAST(sd.Usage AS DECIMAL(10, 2))) AS TotalDuration
                FROM dbo.Session AS s
                INNER JOIN dbo.SessionDetails AS sd ON s.Id = sd.SessionId
                INNER JOIN dbo.IOFIELD AS io ON io.Id = sd.IOFIELDId
                INNER JOIN dbo.Vehicle AS v ON s.VehicleId = v.Id
                LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
				LEFT JOIN dbo.Site st ON ISNULL(vh.SiteId, v.SiteId) = st.Id
                LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
                LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, v.CustomerId) = c.Id
                INNER JOIN dbo.Timezone tz ON st.TimezoneId = tz.Id
                WHERE s.DriverId IS NOT NULL
                    AND io.Name = '0'
                    AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
					AND (@CustomerId IS NULL OR c.Id = @CustomerId)
					AND (@SiteId IS NULL OR st.Id = @SiteId)
					AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
                    AND s.isVOR = 1  -- Only return records where isVOR is True
                GROUP BY s.VehicleId
            ),
            SeatCalc AS (
                SELECT
                    s.VehicleId,
                    ROUND(SUM(CAST(sd.Usage AS DECIMAL(10, 2))), 2) AS TotalSeatHours
                FROM dbo.Session AS s
                INNER JOIN dbo.SessionDetails AS sd ON s.Id = sd.SessionId
                INNER JOIN dbo.IOFIELD AS io ON io.Id = sd.IOFIELDId
                INNER JOIN dbo.Vehicle AS v ON s.VehicleId = v.Id
                LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
				LEFT JOIN dbo.Site st ON ISNULL(vh.SiteId, v.SiteId) = st.Id
                LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
                LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, v.CustomerId) = c.Id
                INNER JOIN dbo.Timezone tz ON st.TimezoneId = tz.Id
                WHERE s.DriverId IS NOT NULL
                    AND (io.Name = '4' OR io.Name = 'SEAT')
                    AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
                    AND (@CustomerId IS NULL OR c.Id = @CustomerId)
                    AND (@SiteId IS NULL OR st.Id = @SiteId)
                    AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
                    AND s.isVOR = 1  -- Only return records where isVOR is True
                GROUP BY s.VehicleId
            ),
            HydraulicCalc AS (
                SELECT
                    s.VehicleId,
                    ROUND(SUM(CAST(sd.Usage AS DECIMAL(10, 2))), 2) AS TotalHydraulicHours
                FROM dbo.Session AS s
                INNER JOIN dbo.SessionDetails AS sd ON s.Id = sd.SessionId
                INNER JOIN dbo.IOFIELD AS io ON io.Id = sd.IOFIELDId
                INNER JOIN dbo.Vehicle AS v ON s.VehicleId = v.Id
                LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
				LEFT JOIN dbo.Site st ON ISNULL(vh.SiteId, v.SiteId) = st.Id
                LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
                LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, st.CustomerId) = c.Id
                INNER JOIN dbo.Timezone tz ON st.TimezoneId = tz.Id
                WHERE s.DriverId IS NOT NULL
                    AND (io.Name = 'HYDR' OR io.Name = 'HYDL' OR io.Name = '1')
                    AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
                    AND (@CustomerId IS NULL OR c.Id = @CustomerId)
                    AND (@SiteId IS NULL OR st.Id = @SiteId)
                    AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
                    AND s.isVOR = 1  -- Only return records where isVOR is True
                GROUP BY s.VehicleId
            ),
            TractionCalc AS (
                SELECT
                    s.VehicleId,
                    ROUND(SUM(CAST(sd.Usage AS DECIMAL(10, 2))), 2) AS TotalTractionHours
                FROM dbo.Session AS s
                INNER JOIN dbo.SessionDetails AS sd ON s.Id = sd.SessionId
                INNER JOIN dbo.IOFIELD AS io ON io.Id = sd.IOFIELDId
                INNER JOIN dbo.Vehicle AS v ON s.VehicleId = v.Id
                LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
				LEFT JOIN dbo.Site st ON ISNULL(vh.SiteId, v.SiteId) = st.Id
                LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
                LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, st.CustomerId) = c.Id
                INNER JOIN dbo.Timezone tz ON st.TimezoneId = tz.Id
                WHERE s.DriverId IS NOT NULL
                    AND (io.Name = 'TRACK' OR io.Name = '2')
                    AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
                    AND (@CustomerId IS NULL OR c.Id = @CustomerId)
                    AND (@SiteId IS NULL OR st.Id = @SiteId)
                    AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
                    AND s.isVOR = 1  -- Only return records where isVOR is True
                GROUP BY s.VehicleId
            )
            SELECT
                NEWID() AS Id,
                main.VehicleId,
                -- de.Id AS DealerId,
                CAST('C187F702-B41E-4578-1B57-5D82395F7FCA' AS UNIQUEIDENTIFIER) AS VORReportCombinedViewId,
                COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TotalDuration, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalDuration,
                COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(SeatCalc.TotalSeatHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalSeatHours,
                COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(HydraulicCalc.TotalHydraulicHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalHydraulicHours,
                COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TractionCalc.TotalTractionHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalTractionHours
            FROM dbo.Session AS main
            INNER JOIN dbo.Vehicle AS v ON v.Id = main.VehicleId
            LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
            LEFT JOIN dbo.Site st ON ISNULL(vh.SiteId, v.SiteId) = st.Id
            LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
            LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, st.CustomerId) = c.Id
            INNER JOIN dbo.Dealer de ON c.DealerId = de.Id
            LEFT JOIN Calculations ON main.VehicleId = Calculations.VehicleId
            LEFT JOIN SeatCalc ON main.VehicleId = SeatCalc.VehicleId
            LEFT JOIN HydraulicCalc ON main.VehicleId = HydraulicCalc.VehicleId
            LEFT JOIN TractionCalc ON main.VehicleId = TractionCalc.VehicleId
			INNER JOIN dbo.Timezone tz ON st.TimezoneId = tz.Id
            WHERE main.DriverID IS NOT NULL 
            AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), main.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
            AND (@CustomerId IS NULL OR c.Id = @CustomerId)
            AND (@SiteId IS NULL OR st.Id = @SiteId)
            AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
            AND main.isVOR = 1  -- Only return records where isVOR is True
            AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR st.Name LIKE '%' + @MultiSearch + '%' OR d.Name LIKE '%' + @MultiSearch + '%')
            GROUP BY main.VehicleId, de.Id, TotalDuration, SeatCalc.TotalSeatHours, HydraulicCalc.TotalHydraulicHours, TractionCalc.TotalTractionHours
            ORDER BY main.VehicleId
            OFFSET @RowOffset ROWS FETCH NEXT @PageSize ROWS ONLY;
        END
    END
    -- Drop the temporary table
    DROP TABLE #VehicleHireHistory;
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetDetailedVORSession]
    @DriverId UNIQUEIDENTIFIER = NULL,
    @VehicleId UNIQUEIDENTIFIER = NULL,
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    -- Create a temporary table to replace the CTE
    CREATE TABLE #VehicleHireHistory (
        VehicleId UNIQUEIDENTIFIER,
        DepartmentId UNIQUEIDENTIFIER,
        SiteId UNIQUEIDENTIFIER,
        CustomerId UNIQUEIDENTIFIER,
        HireTime DATETIME,
        DehireTime DATETIME
    );

    -- Insert data into the temporary table
    INSERT INTO #VehicleHireHistory
    SELECT 
        vh.VehicleId,
        vh.DepartmentId,
        d.SiteId,
        s.CustomerId,
        vh.HireTime,
        vh.DehireTime
    FROM dbo.VehicleHireDehireHistory vh
    JOIN dbo.Department d ON d.Id = vh.DepartmentId
    JOIN dbo.Site s ON s.Id = d.SiteId
    JOIN dbo.Customer c ON c.Id = s.CustomerId
    WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
    AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate);

    IF @ReturnTotalCount = 1
    BEGIN
        SELECT COUNT(DISTINCT s.Id) AS TotalRowCount
        FROM dbo.Session AS s
        INNER JOIN dbo.Driver AS dr ON s.DriverId = dr.Id
        INNER JOIN dbo.Vehicle AS v ON s.VehicleId = v.Id
        LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
        INNER JOIN dbo.Site site ON ISNULL(vh.SiteId, v.SiteId) = site.Id
        INNER JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
        INNER JOIN dbo.Customer c ON ISNULL(vh.CustomerId, v.CustomerId) = c.Id
        INNER JOIN dbo.Timezone tz ON site.TimezoneId = tz.Id
        WHERE s.DriverId IS NOT NULL
          AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
          AND (@DriverId IS NULL OR s.DriverId = @DriverId)
          AND (@VehicleId IS NULL OR s.VehicleId = @VehicleId)
          AND (@CustomerId IS NULL OR c.Id = @CustomerId)
          AND (@SiteId IS NULL OR site.Id = @SiteId)
          AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
          AND s.isVOR = 1;  -- Only return records where isVOR is True
    END
    ELSE
    BEGIN
        DECLARE @RowOffset INT = @PageIndex * @PageSize;

        WITH DetailedCalculations AS (
            SELECT
                NEWID() AS Id,
                s.Id AS SessionId,
                s.DriverId,
                s.VehicleId,
                SUM(CASE WHEN io.Name = '0' THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalDuration,
                SUM(CASE WHEN io.Name = '4' OR io.Name = 'SEAT' THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalSeatHours,
                SUM(CASE WHEN io.Name = 'HYDR' OR io.Name = 'HYDL' OR io.Name = '1' THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalHydraulicHours,
                SUM(CASE WHEN io.Name = 'TRACK' OR io.Name = '2' THEN CAST(sd.Usage AS DECIMAL(10, 2)) ELSE 0 END) AS TotalTractionHours,
                DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.StartTime) AS TimezoneAdjustedStartDateTime,
                DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) AS TimezoneAdjustedEndDateTime
            FROM dbo.Session AS s
            INNER JOIN dbo.SessionDetails AS sd ON s.Id = sd.SessionId
            INNER JOIN dbo.IOFIELD AS io ON io.Id = sd.IOFIELDId
            INNER JOIN dbo.Vehicle AS v ON s.VehicleId = v.Id
            LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
            INNER JOIN dbo.Site site ON ISNULL(vh.SiteId, v.SiteId) = site.Id
            INNER JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
            INNER JOIN dbo.Customer c ON ISNULL(vh.CustomerId, v.CustomerId) = c.Id
            INNER JOIN dbo.Driver dr ON s.DriverId = dr.Id
            INNER JOIN dbo.Timezone tz ON site.TimezoneId = tz.Id
            WHERE s.DriverId IS NOT NULL
              AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), s.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
              AND (@DriverId IS NULL OR s.DriverId = @DriverId)
              AND (@VehicleId IS NULL OR s.VehicleId = @VehicleId)
              AND (@CustomerId IS NULL OR c.Id = @CustomerId)
              AND (@SiteId IS NULL OR site.Id = @SiteId)
              AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
              AND s.isVOR = 1  -- Only return records where isVOR is True
            GROUP BY s.Id, s.DriverId, s.VehicleId, s.StartTime, s.EndTime, tz.UTCOffset
        )
        SELECT
            Id,
            SessionId,
            DriverId,
            VehicleId,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TotalDuration, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalDuration,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TotalSeatHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalSeatHours,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TotalHydraulicHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalHydraulicHours,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(TotalTractionHours, 0), 0), 'HH:mm:ss')), '00:00:00') AS TotalTractionHours,
            TimezoneAdjustedStartDateTime,
            TimezoneAdjustedEndDateTime
        FROM DetailedCalculations
        ORDER BY SessionId
        OFFSET @RowOffset ROWS FETCH NEXT @PageSize ROWS ONLY;
    END
    -- Drop the temporary table
    DROP TABLE #VehicleHireHistory;
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetVORStatus]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    -- @MultiSearch NVARCHAR(4000) = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());
    DECLARE @RowOffset INT = @PageIndex * @PageSize;

    -- Create a temporary table to replace the CTE
    CREATE TABLE #VehicleHireHistory (
        VehicleId UNIQUEIDENTIFIER,
        DepartmentId UNIQUEIDENTIFIER,
        SiteId UNIQUEIDENTIFIER,
        CustomerId UNIQUEIDENTIFIER,
        HireTime DATETIME,
        DehireTime DATETIME
    );

    -- Insert data into the temporary table
    INSERT INTO #VehicleHireHistory
    SELECT 
        vh.VehicleId,
        vh.DepartmentId,
        d.SiteId,
        s.CustomerId,
        vh.HireTime,
        vh.DehireTime
    FROM dbo.VehicleHireDehireHistory vh
    JOIN dbo.Department d ON d.Id = vh.DepartmentId
    JOIN dbo.Site s ON s.Id = d.SiteId
    JOIN dbo.Customer c ON c.Id = s.CustomerId
    WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
    AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate);

    -- If ReturnTotalCount is 1, calculate and return the total number of rows
    IF @ReturnTotalCount = 1
    BEGIN
        SELECT COUNT(1) AS TotalRowCount
        FROM dbo.VORSettingHistory vsh
        INNER JOIN dbo.Vehicle v ON vsh.VehicleId = v.Id
        LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
        LEFT JOIN dbo.Site site ON ISNULL(vh.SiteId, v.SiteId) = site.Id
        LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
        LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, site.CustomerId) = c.Id
        WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
            AND (@SiteId IS NULL OR site.Id = @SiteId)
            AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
            AND (@StartDate IS NULL OR vsh.StartDateTime >= @ActualStartDate)
            AND (@EndDate IS NULL OR vsh.EndDateTime <= @ActualEndDate OR vsh.EndDateTime IS NULL)
        RETURN;
    END

    -- Main query for retrieving the VOR status records
    SELECT
        NEWID() AS Id,  -- Generating a unique ID
        CAST('C187F702-B41E-4578-1B57-5D82395F7FCA' AS UNIQUEIDENTIFIER)  AS VORReportCombinedViewId,  -- Hardcoded value
        vsh.Id AS VORSettingHistoryId,
        CASE 
            WHEN vsh.EndDateTime IS NOT NULL THEN 0  -- Status is 0 if EndDateTime is not null
            ELSE 1  -- Status is 1 if EndDateTime is null
        END AS Status,
		DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), vsh.StartDateTime) AS TimezoneAdjustedStartDateTime,
		DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), vsh.EndDateTime) AS TimezoneAdjustedEndDateTime
    FROM dbo.VORSettingHistory vsh
    INNER JOIN dbo.Vehicle v ON vsh.VehicleId = v.Id
    LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
    LEFT JOIN dbo.Site site ON ISNULL(vh.SiteId, v.SiteId) = site.Id
    LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
    LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, v.CustomerId) = c.Id
	INNER JOIN dbo.Timezone AS tz ON site.TimezoneId = tz.Id
    WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
        AND (@SiteId IS NULL OR site.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
        AND (@StartDate IS NULL OR vsh.StartDateTime >= @ActualStartDate)
        AND (@EndDate IS NULL OR vsh.EndDateTime <= @ActualEndDate OR vsh.EndDateTime IS NULL)
    ORDER BY vsh.Id
    OFFSET @RowOffset ROWS FETCH NEXT @PageSize ROWS ONLY; -- Pagination logic
    -- Drop the temporary table
    DROP TABLE #VehicleHireHistory;
END
GO

CREATE OR ALTER PROCEDURE [dbo].[GetAllMessageHistory]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @MultiSearch NVARCHAR(4000) = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    DECLARE @RowOffset INT = @PageIndex * @PageSize;

    -- Create a temporary table to replace the CTE
    CREATE TABLE #VehicleHireHistory (
        VehicleId UNIQUEIDENTIFIER,
        DepartmentId UNIQUEIDENTIFIER,
        SiteId UNIQUEIDENTIFIER,
        CustomerId UNIQUEIDENTIFIER,
        HireTime DATETIME,
        DehireTime DATETIME
    );

    -- Insert data into the temporary table
    INSERT INTO #VehicleHireHistory
    SELECT 
        vh.VehicleId,
        vh.DepartmentId,
        d.SiteId,
        s.CustomerId,
        vh.HireTime,
        vh.DehireTime
    FROM dbo.VehicleHireDehireHistory vh
    JOIN dbo.Department d ON d.Id = vh.DepartmentId
    JOIN dbo.Site s ON s.Id = d.SiteId
    JOIN dbo.Customer c ON c.Id = s.CustomerId
    WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
    AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate);

    -- If ReturnTotalCount is 1, calculate and return the total number of unique rows
    IF @ReturnTotalCount = 1
    BEGIN
        SELECT COUNT(DISTINCT mh.Id) AS TotalRowCount
        FROM dbo.MessageHistory AS mh
        INNER JOIN dbo.Vehicle AS v ON mh.VehicleId = v.Id
        LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
        LEFT JOIN dbo.Site site ON ISNULL(vh.SiteId, v.SiteId) = site.Id
        LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
        LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, site.CustomerId) = c.Id
        INNER JOIN dbo.Timezone AS tz ON site.TimezoneId = tz.Id
        WHERE mh.Id IS NOT NULL
          AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), mh.SentTimestamp) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
          AND (@CustomerId IS NULL OR c.Id = @CustomerId)
          AND (@SiteId IS NULL OR site.Id = @SiteId)
          AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
          AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR site.Name LIKE '%' + @MultiSearch + '%' OR d.Name LIKE '%' + @MultiSearch + '%');
    END
    ELSE
    BEGIN
        -- Use ROW_NUMBER to ensure unique MessageHistoryId values
        WITH UniqueMessageHistory AS (
            SELECT
                mh.Id AS MessageHistoryId,
                DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), mh.SentTimestamp) AS TimezoneAdjustedSentTime,
                DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), mh.DeliveredTimestamp) AS TimezoneAdjustedDeliveryTime,
                ROW_NUMBER() OVER (PARTITION BY mh.Id ORDER BY mh.SentTimestamp) AS RowNum
            FROM dbo.MessageHistory AS mh
            INNER JOIN dbo.Vehicle AS v ON mh.VehicleId = v.Id
            LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
			LEFT JOIN dbo.Site site ON ISNULL(vh.SiteId, v.SiteId) = site.Id
			LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
			LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, site.CustomerId) = c.Id
            INNER JOIN dbo.Timezone AS tz ON site.TimezoneId = tz.Id
            WHERE mh.Id IS NOT NULL
                AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), mh.SentTimestamp) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
                AND (@CustomerId IS NULL OR c.Id = @CustomerId)
                AND (@SiteId IS NULL OR site.Id = @SiteId)
                AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
                AND (@MultiSearch IS NULL OR v.HireNo LIKE '%' + @MultiSearch + '%' OR site.Name LIKE '%' + @MultiSearch + '%' OR d.Name LIKE '%' + @MultiSearch + '%')
        )
        -- Select only the first occurrence of each MessageHistoryId
        SELECT
            NEWID() AS Id,
            MessageHistoryId,
            TimezoneAdjustedSentTime,
            TimezoneAdjustedDeliveryTime
        FROM UniqueMessageHistory
        WHERE RowNum = 1
        ORDER BY MessageHistoryId
        OFFSET @RowOffset ROWS FETCH NEXT @PageSize ROWS ONLY;
    END
    -- Drop the temporary table
    DROP TABLE #VehicleHireHistory;
END;
GO

CREATE OR ALTER VIEW VehicleLastGPSLocationView AS
SELECT 
    VG.VehicleId,
    VG.SessionId,
    VG.Longitude,
    VG.Latitude,
    VG.GPSDateTime
FROM 
    dbo.VehicleGPS VG
INNER JOIN 
    (
        -- Subquery to get the last GPS timestamp per vehicle
        SELECT 
            VehicleId, 
            MAX(GPSDateTime) AS LastGPSDateTime
        FROM 
            dbo.VehicleGPS
        GROUP BY 
            VehicleId
    ) AS LastGPS
ON 
    VG.VehicleId = LastGPS.VehicleId
    AND VG.GPSDateTime = LastGPS.LastGPSDateTime;

GO

CREATE OR ALTER PROCEDURE [dbo].[GetOnDemandAuthorisation]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    -- Create a temporary table to replace the CTE
    CREATE TABLE #VehicleHireHistory (
        VehicleId UNIQUEIDENTIFIER,
        DepartmentId UNIQUEIDENTIFIER,
        SiteId UNIQUEIDENTIFIER,
        CustomerId UNIQUEIDENTIFIER,
        HireTime DATETIME,
        DehireTime DATETIME
    );

    -- Insert data into the temporary table
    INSERT INTO #VehicleHireHistory
    SELECT 
        vh.VehicleId,
        vh.DepartmentId,
        d.SiteId,
        s.CustomerId,
        vh.HireTime,
        vh.DehireTime
    FROM dbo.VehicleHireDehireHistory vh
    JOIN dbo.Department d ON d.Id = vh.DepartmentId
    JOIN dbo.Site s ON s.Id = d.SiteId
    JOIN dbo.Customer c ON c.Id = s.CustomerId
    WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
    AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate);

    IF @ReturnTotalCount = 1
    BEGIN
        -- Get the total count of OnDemandSession records
        SELECT COUNT(DISTINCT ods.Id) AS TotalRowCount
        FROM dbo.OnDemandSession AS ods
        INNER JOIN dbo.Vehicle AS v ON ods.VehicleId = v.Id
        LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
        LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
        LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
        LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, v.CustomerId) = c.Id
        INNER JOIN dbo.Timezone AS tz ON s.TimezoneId = tz.Id
        WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
          AND (@SiteId IS NULL OR s.Id = @SiteId)
          AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
          AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), ods.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate);
    END
    ELSE
    BEGIN
        DECLARE @RowOffset INT = @PageIndex * @PageSize;

        -- Fetch the paginated results with the necessary filters
        WITH OnDemandAuthorisationResults AS (
            SELECT
                NEWID() AS Id, -- Generate a new ID for each record
                ods.Id AS OnDemandSessionId,
                DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), ods.StartTime) AS TimezoneAdjustedStartTime,
                DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), ods.EndTime) AS TimezoneAdjustedEndTime
            FROM dbo.OnDemandSession AS ods
            INNER JOIN dbo.Vehicle AS v ON ods.VehicleId = v.Id
            LEFT JOIN #VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
            LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, v.SiteId) = s.Id
            LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
            LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, v.CustomerId) = c.Id
            INNER JOIN dbo.Timezone AS tz ON s.TimezoneId = tz.Id
            WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
              AND (@SiteId IS NULL OR s.Id = @SiteId)
              AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
              AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), ods.StartTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
        )
        SELECT 
            Id,
            OnDemandSessionId,
            TimezoneAdjustedStartTime,
            TimezoneAdjustedEndTime
        FROM OnDemandAuthorisationResults
        ORDER BY OnDemandSessionId
        OFFSET @RowOffset ROWS FETCH NEXT @PageSize ROWS ONLY;
    END
    -- Drop the temporary table
    DROP TABLE #VehicleHireHistory;
END;
GO

CREATE OR ALTER PROCEDURE [dbo].[GetLoggedHoursVersusSeatHours]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @MultiSearch NVARCHAR(4000) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    -- CTE for VehicleHireHistory
    WITH VehicleHireHistory AS (
        SELECT 
            vh.VehicleId,
            vh.DepartmentId,
            d.SiteId,
            s.CustomerId,
            vh.HireTime,
            vh.DehireTime
        FROM dbo.VehicleHireDehireHistory vh
        JOIN dbo.Department d ON d.Id = vh.DepartmentId
        JOIN dbo.Site s ON s.Id = d.SiteId
        JOIN dbo.Customer c ON c.Id = s.CustomerId
        WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
        AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate)
    )

    -- Main Query
    SELECT
        Id = NEWID(),
		DATEPART(YEAR, main.EndTime) * 368 + DATEPART(MONTH, main.EndTime) * 31 + DATEPART(DAY, CONVERT(varchar(19), DATEADD(HOUR, tz.UTCOffset, main.EndTime), 120)) AS [Order],
        DATEPART(YEAR, main.EndTime) AS [Year],
        DATEPART(MONTH, main.EndTime) AS [Month],
        DATEPART(DAY, CONVERT(VARCHAR(19), DATEADD(HOUR, tz.UTCOffset, main.EndTime), 120)) AS [Day],
        ROUND(SUM(CASE WHEN io.Name = '0' THEN CAST(sd.Usage AS DECIMAL(10, 2)) / 3600 ELSE 0 END), 2) AS [LoggedHours],
        ROUND(SUM(CASE WHEN io.Name = '4' OR io.Name = 'SEAT' THEN CAST(sd.Usage AS DECIMAL(10, 2)) / 3600 ELSE 0 END), 2) AS [SeatHours],
        ROUND(SUM(CASE WHEN io.Name = 'HYDR' OR io.Name = 'HYDL' OR io.Name = '1' THEN CAST(sd.Usage AS DECIMAL(10, 2)) / 3600 ELSE 0 END), 2) AS [HydraulicHours],
        ROUND(SUM(CASE WHEN io.Name = 'TRACK' OR io.Name = '2' THEN CAST(sd.Usage AS DECIMAL(10, 2)) / 3600 ELSE 0 END), 2) AS [TractionHours]
    FROM
        dbo.Session AS main
    LEFT JOIN dbo.SessionDetails AS sd ON main.Id = sd.SessionID
    LEFT JOIN dbo.IOFIELD AS io ON io.ID = sd.IOFIELDId
    LEFT JOIN dbo.Vehicle v ON v.Id = main.VehicleId
    LEFT JOIN VehicleHireHistory vh ON vh.VehicleId = v.Id  -- Use vehicle hire history
    LEFT JOIN dbo.Department d ON ISNULL(vh.DepartmentId, v.DepartmentId) = d.Id
    LEFT JOIN dbo.Site s ON ISNULL(vh.SiteId, d.SiteId) = s.Id
    LEFT JOIN dbo.Customer c ON ISNULL(vh.CustomerId, s.CustomerId) = c.Id
    LEFT JOIN dbo.Dealer de ON de.Id = c.DealerId
    LEFT JOIN dbo.Timezone tz ON tz.Id = s.TimezoneId
    WHERE
        main.DriverId IS NOT NULL
        AND (io.Name = '0' OR io.Name = '1' OR io.Name = '2' OR io.Name = '4' OR io.Name = 'SEAT' OR io.Name = 'HYDR' OR io.Name = 'HYDL' OR io.Name = 'TRACK')
        AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), main.EndTime) BETWEEN DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualStartDate) AND DATEADD(HOUR, ISNULL(tz.UTCOffset, 0), @ActualEndDate)
        AND (@CustomerId IS NULL OR c.Id = @CustomerId)
        AND (@SiteId IS NULL OR s.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
    GROUP BY
        DATEPART(YEAR, main.EndTime),
        DATEPART(MONTH, main.EndTime),
        DATEPART(DAY, CONVERT(VARCHAR(19), DATEADD(HOUR, tz.UTCOffset, main.EndTime), 120))
    ORDER BY [Year], [Month], [Day]
END;
GO

-- Vehicle Supervisor View
CREATE OR ALTER VIEW [dbo].[VehicleSupervisorsView] AS
WITH DistinctVehicleCards AS
(
    SELECT
        PVNCA.Id AS Id,
        PVNCA.VehicleId,
        PVNCA.Id AS PerVehicleNormalCardAccessId,
        PVNCA.CardId,
        -- Assign a row number for each distinct (VehicleId, CardId) group
        ROW_NUMBER() OVER (PARTITION BY PVNCA.VehicleId, PVNCA.CardId ORDER BY PVNCA.Id) AS RN
    FROM dbo.PerVehicleNormalCardAccess PVNCA
    INNER JOIN dbo.Permission P ON PVNCA.PermissionId = P.Id
    WHERE P.LevelName = '1'
)
-- Select only the first occurrence per (VehicleId, CardId) pair
SELECT
    Id,
    VehicleId,
    PerVehicleNormalCardAccessId
FROM DistinctVehicleCards
WHERE RN = 1;
GO

CREATE OR ALTER VIEW [dbo].[AllChecklistResultView] AS
SELECT
    cr.Id,
    cr.Id AS ChecklistResultId,
    CASE WHEN cr.EndTime IS NOT NULL THEN 1 ELSE 0 END AS CheckComplete,
    cr.EndTime AS CompletionTime,
    c.DealerId,
    site.Name AS Site,
    d.Name AS Department,
    cr.StartTime AS TimezoneAdjustedChecklistStartTime,
    CASE 
        WHEN EXISTS (
            SELECT 1 
            FROM dbo.ChecklistDetail cd
            INNER JOIN dbo.PreOperationalChecklist poc ON cd.PreOperationalChecklistId = poc.Id
            WHERE cd.ChecklistResultId = cr.Id 
            AND poc.Critical = 1 
            AND cd.Answer != poc.ExpectedAnswer
        ) THEN 1
        ELSE 0
    END AS HasCriticalQuestions
FROM dbo.ChecklistResult cr
INNER JOIN dbo.Session s ON cr.SessionId1 = s.Id
INNER JOIN dbo.Vehicle v ON s.VehicleId = v.Id
INNER JOIN dbo.Site site ON v.SiteId = site.Id
INNER JOIN dbo.Department d ON v.DepartmentId = d.Id
INNER JOIN dbo.Customer c ON v.CustomerId = c.Id
GO

CREATE OR ALTER PROCEDURE [dbo].[GetUnitUtilisationData]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @StartDate DATETIME = NULL,
    @EndDate DATETIME = NULL,
    @PageIndex INT = 0,
    @PageSize INT = 10,
    @ReturnTotalCount BIT = 0,
    @MultiSearch NVARCHAR(4000) = NULL,
    @IsUtilized BIT = 1
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @RowOffset INT = @PageIndex * @PageSize;
    DECLARE @ActualStartDate DATETIME = COALESCE(@StartDate, DATEADD(MONTH, -3, GETUTCDATE()));
    DECLARE @ActualEndDate DATETIME = COALESCE(@EndDate, GETUTCDATE());

    -- Create a temporary table for vehicle hire history
    CREATE TABLE #VehicleHireHistory (
        VehicleId UNIQUEIDENTIFIER,
        DepartmentId UNIQUEIDENTIFIER,
        SiteId UNIQUEIDENTIFIER,
        CustomerId UNIQUEIDENTIFIER,
        HireTime DATETIME,
        DehireTime DATETIME
    );

    -- Insert data into the temporary table
    INSERT INTO #VehicleHireHistory
    SELECT 
        vh.VehicleId,
        vh.DepartmentId,
        d.SiteId,
        s.CustomerId,
        vh.HireTime,
        vh.DehireTime
    FROM dbo.VehicleHireDehireHistory vh
    JOIN dbo.Department d ON d.Id = vh.DepartmentId
    JOIN dbo.Site s ON s.Id = d.SiteId
    JOIN dbo.Customer c ON c.Id = s.CustomerId
    WHERE (vh.HireTime IS NULL OR vh.HireTime <= @ActualEndDate)
    AND (vh.DehireTime IS NULL OR vh.DehireTime >= @ActualStartDate);

    -- Create table for vehicle hierarchy data
    CREATE TABLE #VehicleHierarchy (
        VehicleId UNIQUEIDENTIFIER,
        DepartmentId UNIQUEIDENTIFIER,
        SiteId UNIQUEIDENTIFIER,
        CustomerId UNIQUEIDENTIFIER,
        DealerId UNIQUEIDENTIFIER,
        TimezoneOffset INT
    );

    -- Get all vehicles matching the filter criteria
    INSERT INTO #VehicleHierarchy
    SELECT DISTINCT
        v.Id AS VehicleId,
        ISNULL(vh.DepartmentId, d.Id) AS DepartmentId,
        ISNULL(vh.SiteId, s.Id) AS SiteId,
        ISNULL(vh.CustomerId, c.Id) AS CustomerId,
        de.Id AS DealerId,
        tz.UTCOffset AS TimezoneOffset
    FROM dbo.Vehicle v
    LEFT JOIN #VehicleHireHistory vh ON v.Id = vh.VehicleId
    INNER JOIN dbo.Department d ON v.DepartmentId = d.Id
    INNER JOIN dbo.Site s ON d.SiteId = s.Id
    INNER JOIN dbo.Customer c ON s.CustomerId = c.Id
    INNER JOIN dbo.Dealer de ON c.DealerId = de.Id
    INNER JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
    WHERE (@CustomerId IS NULL OR ISNULL(vh.CustomerId, c.Id) = @CustomerId)
    AND (@SiteId IS NULL OR ISNULL(vh.SiteId, s.Id) = @SiteId)
    AND (@DepartmentId IS NULL OR ISNULL(vh.DepartmentId, d.Id) = @DepartmentId)
    AND (@MultiSearch IS NULL 
        OR v.HireNo LIKE '%' + @MultiSearch + '%' 
        OR v.SerialNo LIKE '%' + @MultiSearch + '%'
        OR s.Name LIKE '%' + @MultiSearch + '%' 
        OR d.Name LIKE '%' + @MultiSearch + '%');

    -- Create temporary table to store the vehicles with session data
    CREATE TABLE #VehiclesWithSessions (
        VehicleId UNIQUEIDENTIFIER PRIMARY KEY
    );

    -- Find vehicles with sessions in the date range
    INSERT INTO #VehiclesWithSessions
    SELECT DISTINCT s.VehicleId
    FROM #VehicleHierarchy vh
    INNER JOIN dbo.Session s ON vh.VehicleId = s.VehicleId
    LEFT JOIN #VehicleHireHistory vhh ON s.VehicleId = vhh.VehicleId
    WHERE s.DriverId IS NOT NULL
    AND s.isVOR = 0
    AND DATEADD(HOUR, vh.TimezoneOffset, s.EndTime) BETWEEN DATEADD(HOUR, vh.TimezoneOffset, @ActualStartDate) AND DATEADD(HOUR, vh.TimezoneOffset, @ActualEndDate)
    AND (
        -- Include sessions where vehicle hire/dehire history overlaps with session time
        (vhh.VehicleId IS NOT NULL AND 
         s.StartTime < ISNULL(vhh.DehireTime, '9999-12-31') AND 
         s.EndTime > ISNULL(vhh.HireTime, '1900-01-01'))
        -- Include sessions where there's no hire/dehire history for this vehicle
        OR vhh.VehicleId IS NULL
    )
    AND (
        @CustomerId IS NULL OR 
        vh.CustomerId = @CustomerId
    )
    AND (
        @SiteId IS NULL OR 
        vh.SiteId = @SiteId
    )
    AND (
        @DepartmentId IS NULL OR 
        vh.DepartmentId = @DepartmentId
    );

    -- Handle return total count
    IF @ReturnTotalCount = 1
    BEGIN
        IF @IsUtilized = 1
            SELECT COUNT(*) AS TotalRowCount FROM #VehiclesWithSessions;
        ELSE
            SELECT COUNT(*) AS TotalRowCount 
            FROM #VehicleHierarchy vh
            WHERE NOT EXISTS (SELECT 1 FROM #VehiclesWithSessions vws WHERE vws.VehicleId = vh.VehicleId);
        
        DROP TABLE #VehicleHierarchy;
        DROP TABLE #VehiclesWithSessions;
        DROP TABLE #VehicleHireHistory;
        RETURN;
    END;

    IF @IsUtilized = 1
    BEGIN
        -- Create table for time slot usage data
        CREATE TABLE #TimeSlotUsage (
            VehicleId UNIQUEIDENTIFIER PRIMARY KEY,
            FleetTotal DECIMAL(10, 2),
            [NineToTwelveAM] DECIMAL(10, 2),
            [TwelveToThreePM] DECIMAL(10, 2),
            [ThreeToSixAM] DECIMAL(10, 2),
            [ThreeToSixPM] DECIMAL(10, 2),
            [SixToNineAM] DECIMAL(10, 2),
            [SixToNinePM] DECIMAL(10, 2),
            [TwelveToThreeAM] DECIMAL(10, 2),
            [NineToTwelvePM] DECIMAL(10, 2)
        );

        -- Calculate time slot usage for vehicles with sessions
        INSERT INTO #TimeSlotUsage
        SELECT 
            s.VehicleId,
            SUM(CAST(sd.Usage AS DECIMAL(10, 2))) AS FleetTotal,
            SUM(
                CASE 
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 9 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 12)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (3.0 / DATEDIFF(HOUR, s.StartTime, s.EndTime))
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 9 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 9 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 12)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, 9, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)), 0)), DATEADD(HOUR, vh.TimezoneOffset, s.EndTime))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 9 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 12 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 12)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime), DATEADD(HOUR, 12, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)), 0)))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 9 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 12 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 9 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 12)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2))
                    ELSE 0 
                END
            ) AS [NineToTwelveAM],
            SUM(
                CASE 
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 12 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 15)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (3.0 / DATEDIFF(HOUR, s.StartTime, s.EndTime))
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 12 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 12 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 15)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, 12, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)), 0)), DATEADD(HOUR, vh.TimezoneOffset, s.EndTime))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 12 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 15 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 15)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime), DATEADD(HOUR, 15, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)), 0)))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 12 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 15 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 12 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 15)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2))
                    ELSE 0 
                END
            ) AS [TwelveToThreePM],
            SUM(
                CASE 
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 3 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 6)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (3.0 / DATEDIFF(HOUR, s.StartTime, s.EndTime))
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 3 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 3 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 6)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, 3, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)), 0)), DATEADD(HOUR, vh.TimezoneOffset, s.EndTime))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 3 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 6 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 6)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime), DATEADD(HOUR, 6, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)), 0)))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 3 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 6 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 3 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 6)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2))
                    ELSE 0 
                END
            ) AS [ThreeToSixAM],
            SUM(
                CASE 
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 15 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 18)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (3.0 / DATEDIFF(HOUR, s.StartTime, s.EndTime))
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 15 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 15 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 18)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, 15, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)), 0)), DATEADD(HOUR, vh.TimezoneOffset, s.EndTime))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 15 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 18 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 18)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime), DATEADD(HOUR, 18, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)), 0)))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 15 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 18 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 15 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 18)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2))
                    ELSE 0 
                END
            ) AS [ThreeToSixPM],
            SUM(
                CASE 
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 6 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 9)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (3.0 / DATEDIFF(HOUR, s.StartTime, s.EndTime))
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 6 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 6 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 9)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, 6, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)), 0)), DATEADD(HOUR, vh.TimezoneOffset, s.EndTime))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 6 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 9 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 9)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime), DATEADD(HOUR, 9, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)), 0)))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 6 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 9 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 6 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 9)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2))
                    ELSE 0 
                END
            ) AS [SixToNineAM],
            SUM(
                CASE 
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 18 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 21)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (3.0 / DATEDIFF(HOUR, s.StartTime, s.EndTime))
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 18 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 18 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 21)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, 18, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)), 0)), DATEADD(HOUR, vh.TimezoneOffset, s.EndTime))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 18 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 21 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 21)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime), DATEADD(HOUR, 21, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)), 0)))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 18 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 21 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 18 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 21)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2))
                    ELSE 0 
                END
            ) AS [SixToNinePM],
            SUM(
                CASE 
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 0 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 3)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (3.0 / DATEDIFF(HOUR, s.StartTime, s.EndTime))
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 0 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 0 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 3)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, 0, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)), 0)), DATEADD(HOUR, vh.TimezoneOffset, s.EndTime))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 0 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 3 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 3)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime), DATEADD(HOUR, 3, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)), 0)))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 0 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 3 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 0 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 3)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2))
                    ELSE 0 
                END
            ) AS [TwelveToThreeAM],
            SUM(
                CASE 
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 21 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 24)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (3.0 / DATEDIFF(HOUR, s.StartTime, s.EndTime))
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 21 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 21 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 24)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, 21, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)), 0)), DATEADD(HOUR, vh.TimezoneOffset, s.EndTime))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 21 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 24 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 24)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2)) * (DATEDIFF(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime), DATEADD(HOUR, 24, DATEADD(DAY, DATEDIFF(DAY, 0, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)), 0)))) / DATEDIFF(HOUR, s.StartTime, s.EndTime)
                    WHEN (DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) >= 21 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.StartTime)) < 24 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) >= 21 AND DATEPART(HOUR, DATEADD(HOUR, vh.TimezoneOffset, s.EndTime)) < 24)
                        THEN CAST(sd.Usage AS DECIMAL(10, 2))
                    ELSE 0 
                END
            ) AS [NineToTwelvePM]
        FROM dbo.Session s
        INNER JOIN #VehicleHierarchy vh ON s.VehicleId = vh.VehicleId
        INNER JOIN dbo.SessionDetails sd ON s.Id = sd.SessionId
        INNER JOIN dbo.IOFIELD io ON io.Id = sd.IOFIELDId
        LEFT JOIN #VehicleHireHistory vhh ON s.VehicleId = vhh.VehicleId
        WHERE s.DriverId IS NOT NULL
        AND DATEADD(HOUR, vh.TimezoneOffset, s.EndTime) BETWEEN DATEADD(HOUR, vh.TimezoneOffset, @ActualStartDate) AND DATEADD(HOUR, vh.TimezoneOffset, @ActualEndDate)
        AND s.isVOR = 0
        AND io.Name = '0' -- Ignition hours
        AND (
            -- Include sessions where vehicle hire/dehire history overlaps with session time
            (vhh.VehicleId IS NOT NULL AND 
             s.StartTime < ISNULL(vhh.DehireTime, '9999-12-31') AND 
             s.EndTime > ISNULL(vhh.HireTime, '1900-01-01'))
            -- Include sessions where there's no hire/dehire history for this vehicle
            OR vhh.VehicleId IS NULL
        )
        AND (
            @CustomerId IS NULL OR 
            vh.CustomerId = @CustomerId
        )
        AND (
            @SiteId IS NULL OR 
            vh.SiteId = @SiteId
        )
        AND (
            @DepartmentId IS NULL OR 
            vh.DepartmentId = @DepartmentId
        )
        GROUP BY s.VehicleId;

        -- Return utilized units data
        SELECT 
            NEWID() AS Id,
            vh.VehicleId,
            vh.DealerId,
            CAST('C547F788-B08E-4159-9B45-5D25195F7FCA' AS UNIQUEIDENTIFIER) AS GeneralProductivityViewId,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(tu.FleetTotal, 0), 0), 'HH:mm:ss')), '00:00:00') AS FleetTotal,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(tu.[TwelveToThreePM], 0), 0), 'HH:mm:ss')), '00:00:00') AS TwelveToThreePM,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(tu.[TwelveToThreeAM], 0), 0), 'HH:mm:ss')), '00:00:00') AS TwelveToThreeAM,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(tu.[ThreeToSixAM], 0), 0), 'HH:mm:ss')), '00:00:00') AS ThreeToSixAM,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(tu.[ThreeToSixPM], 0), 0), 'HH:mm:ss')), '00:00:00') AS ThreeToSixPM,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(tu.[SixToNineAM], 0), 0), 'HH:mm:ss')), '00:00:00') AS SixToNineAM,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(tu.[SixToNinePM], 0), 0), 'HH:mm:ss')), '00:00:00') AS SixToNinePM,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(tu.[NineToTwelveAM], 0), 0), 'HH:mm:ss')), '00:00:00') AS NineToTwelveAM,
            COALESCE(CONVERT(VARCHAR, FORMAT(DATEADD(SECOND, ISNULL(tu.[NineToTwelvePM], 0), 0), 'HH:mm:ss')), '00:00:00') AS NineToTwelvePM
        FROM #VehicleHierarchy vh
        INNER JOIN #VehiclesWithSessions vws ON vh.VehicleId = vws.VehicleId
        LEFT JOIN #TimeSlotUsage tu ON vh.VehicleId = tu.VehicleId
        ORDER BY vh.VehicleId
        OFFSET @RowOffset ROWS 
        FETCH NEXT @PageSize ROWS ONLY;

        -- Cleanup
        DROP TABLE #TimeSlotUsage;
    END
    ELSE
    BEGIN
        -- Return unutilized units data
        SELECT 
            NEWID() AS Id,
            vh.VehicleId,
            CAST('C547F788-B08E-4159-9B45-5D25195F7FCA' AS UNIQUEIDENTIFIER) AS GeneralProductivityViewId
        FROM #VehicleHierarchy vh
        WHERE NOT EXISTS (SELECT 1 FROM #VehiclesWithSessions vws WHERE vws.VehicleId = vh.VehicleId)
        ORDER BY vh.VehicleId
        OFFSET @RowOffset ROWS 
        FETCH NEXT @PageSize ROWS ONLY;
    END;

    -- Cleanup
    DROP TABLE #VehicleHierarchy;
    DROP TABLE #VehiclesWithSessions;
    DROP TABLE #VehicleHireHistory;
END;
GO

-- Create GetDashboardDriverCard stored procedure
-- Fix GetDashboardDriverCard stored procedure to include timezone handling
CREATE OR ALTER PROCEDURE [dbo].[GetDashboardDriverCard]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @DealerId UNIQUEIDENTIFIER = NULL,
    @ReferenceDate DATETIME = NULL
AS
BEGIN
    SET NOCOUNT ON;

    SET @ReferenceDate = COALESCE(@ReferenceDate, GETUTCDATE());
    
    WITH PersonData AS (
        SELECT 
            NEWID() AS [Id], 
            (CASE WHEN GROUPING(c.Id) = 0 THEN c.Id ELSE NULL END) AS CustomerId,
            (CASE WHEN GROUPING(s.Id) = 0 THEN s.Id ELSE NULL END) AS SiteId,
            (CASE WHEN GROUPING(d.Id) = 0 THEN d.Id ELSE NULL END) AS DepartmentId,
            (CASE WHEN GROUPING(de.Id) = 0 THEN de.Id ELSE NULL END) AS DealerId,
            COUNT(DISTINCT dr.Id) AS DriversCount,
            COUNT(CASE WHEN DATEADD(HOUR, tz.UTCOffset, ld.ExpiryDate) < @ReferenceDate AND p.DriverId IS NOT NULL THEN 1 ELSE NULL END) AS ExpiredLicensesCount,
            COUNT(CASE WHEN dr.LicenceDetailId IS NULL AND p.DriverId IS NOT NULL THEN 1 ELSE NULL END) AS NoLicenseDriversCount,
            COUNT(CASE WHEN DATEADD(HOUR, tz.UTCOffset, dr.LastSessionDate) < DATEADD(DAY, -7, @ReferenceDate) AND p.DriverId IS NOT NULL THEN 1 ELSE NULL END) AS NonActiveDriversCountLastWeek
        FROM dbo.Person p
            LEFT JOIN dbo.Customer c ON p.CustomerId = c.Id
            LEFT JOIN dbo.Dealer de ON c.DealerId = de.Id
            LEFT JOIN dbo.Driver dr ON p.DriverId = dr.Id
            LEFT JOIN dbo.Department d ON p.DepartmentId = d.Id
            LEFT JOIN dbo.Site s ON p.SiteId = s.Id
            LEFT JOIN dbo.LicenceDetail ld ON dr.LicenceDetailId = ld.Id
            LEFT JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
        WHERE
            (@CustomerId IS NULL OR p.CustomerId = @CustomerId)
            AND (@SiteId IS NULL OR p.SiteId = @SiteId)
            AND (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId)
            AND (@DealerId IS NULL OR c.DealerId = @DealerId)
        GROUP BY 
            GROUPING SETS (
                (de.Id, c.Id, s.Id, d.Id),  -- All levels
                (de.Id, c.Id, s.Id),       -- Customer and Site
                (de.Id, c.Id, d.Id),       -- Customer and Department
                (s.Id, d.Id),              -- Site and Department
                (de.Id, c.Id),             -- Customer only
                (s.Id),                    -- Site only
                (d.Id),                    -- Department only
                ()                         -- Total aggregation
            )
    )
    SELECT 
        NEWID() as Id,
        DealerId,
        CustomerId,
        SiteId,
        DepartmentId,
        SUM(DriversCount) AS DriversCount,
        SUM(ExpiredLicensesCount) AS ExpiredLicensesCount,
        SUM(NoLicenseDriversCount) AS NoLicenseDriversCount,
        SUM(NonActiveDriversCountLastWeek) AS NonActiveDriversCountLastWeek
    FROM PersonData
    GROUP BY
        DealerId,
        CustomerId,
        SiteId,
        DepartmentId;
END
GO 

-- Create GetDashboardVehicleCard stored procedure
-- Fix GetDashboardVehicleCard stored procedure to include timezone handling
CREATE OR ALTER PROCEDURE [dbo].[GetDashboardVehicleCard]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @DealerId UNIQUEIDENTIFIER = NULL,
    @ReferenceDate DATETIME = NULL
AS
BEGIN
    SET NOCOUNT ON;

    SET @ReferenceDate = COALESCE(@ReferenceDate, GETUTCDATE());

    WITH SessionDateBoundaries AS (
        SELECT 
            DATEADD(HOUR, -24, @ReferenceDate) AS LastTwentyFourHours,
            DATEADD(HOUR, -72, @ReferenceDate) AS LastSeventyTwoHours,
            @ReferenceDate AS CurrentDay,
            DATEADD(DAY, -1, @ReferenceDate) AS LastDay,
            DATEADD(DAY, -2, @ReferenceDate) AS TwoDaysAgo,
            DATEADD(DAY, -7, @ReferenceDate) AS LastWeek,
            DATEADD(DAY, -28, @ReferenceDate) AS LastFourWeeks,
            DATEADD(DAY, 1, CAST(CAST(@ReferenceDate AS DATE) AS DATETIME)) AS EndOfDay
    ),
    VehicleBaseData AS (
        SELECT 
            v.Id AS VehicleId,
            v.LastSessionDate,
            v.DepartmentId,
            d.SiteId,
            s.CustomerId,
            c.DealerId,
            tz.UTCOffset,
            ISNULL(vos.VORStatus, 0) AS VORStatus,
            CASE WHEN v.LastSessionDate < db.LastTwentyFourHours THEN 1 ELSE 0 END AS IsInactiveTwentyFourHours,
            CASE WHEN v.LastSessionDate < db.LastSeventyTwoHours THEN 1 ELSE 0 END AS IsInactiveSeventyTwoHours
        FROM 
            dbo.Vehicle v
            CROSS JOIN SessionDateBoundaries db
            INNER JOIN dbo.Department d ON v.DepartmentId = d.Id
            INNER JOIN dbo.Site s ON d.SiteId = s.Id
            INNER JOIN dbo.Customer c ON s.CustomerId = c.Id
            INNER JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
            LEFT JOIN dbo.VehicleOtherSettings vos ON v.VehicleOtherSettingsId = vos.Id
        WHERE
            (@CustomerId IS NULL OR s.CustomerId = @CustomerId)
            AND (@SiteId IS NULL OR d.SiteId = @SiteId)
            AND (@DepartmentId IS NULL OR v.DepartmentId = @DepartmentId)
            AND (@DealerId IS NULL OR c.DealerId = @DealerId)
    ),
    ImpactStats AS (
        SELECT 
            v.VehicleId,
            SUM(CASE 
                WHEN DATEADD(HOUR, v.UTCOffset, imp.ImpactDateTime) >= CAST(CAST(@ReferenceDate AS DATE) AS DATETIME)
                    AND DATEADD(HOUR, v.UTCOffset, imp.ImpactDateTime) < DATEADD(DAY, 1, CAST(CAST(@ReferenceDate AS DATE) AS DATETIME))
                    AND imp.Threshold != 0 
                    AND imp.ShockValue > (10 * imp.Threshold) 
                THEN 1 ELSE 0 END) AS RedImpactCountToday,
            SUM(CASE 
                WHEN DATEADD(HOUR, v.UTCOffset, imp.ImpactDateTime) >= db.LastDay 
                    AND DATEADD(HOUR, v.UTCOffset, imp.ImpactDateTime) < @ReferenceDate
                    AND imp.Threshold != 0 
                    AND imp.ShockValue > (10 * imp.Threshold) 
                THEN 1 ELSE 0 END) AS RedImpactCountYesterday,
            CAST(SUM(CASE 
                WHEN DATEADD(HOUR, v.UTCOffset, imp.ImpactDateTime) >= db.LastWeek
                    AND DATEADD(HOUR, v.UTCOffset, imp.ImpactDateTime) < db.EndOfDay
                    AND imp.Threshold != 0 
                    AND imp.ShockValue > (10 * imp.Threshold) 
                THEN 1 ELSE 0 END) AS FLOAT) / 7.0 AS RedImpactDailyAverageCountLastWeek,
            CAST(SUM(CASE 
                WHEN DATEADD(HOUR, v.UTCOffset, imp.ImpactDateTime) >= db.LastFourWeeks
                    AND DATEADD(HOUR, v.UTCOffset, imp.ImpactDateTime) < db.EndOfDay
                    AND imp.Threshold != 0 
                    AND imp.ShockValue > (10 * imp.Threshold) 
                THEN 1 ELSE 0 END) AS FLOAT) / 28.0 AS RedImpactDailyAverageCountLastFourWeeks
        FROM 
            VehicleBaseData v
            CROSS JOIN SessionDateBoundaries db
            LEFT JOIN dbo.Session sess ON v.VehicleId = sess.VehicleId
            LEFT JOIN dbo.Impact imp ON sess.Id = imp.SessionId
        GROUP BY 
            v.VehicleId
    ),
    OverlappingSessions AS (
        SELECT 
            sess1.VehicleId,
            COUNT(*) AS OverlappingSessionCount
        FROM 
            dbo.Session sess1
            INNER JOIN dbo.Session sess2 ON sess1.VehicleId = sess2.VehicleId
            CROSS JOIN SessionDateBoundaries db
        WHERE 
            sess1.StartTime < sess2.StartTime
            AND sess1.EndTime > sess2.StartTime
            AND sess1.StartTime >= db.LastTwentyFourHours
        GROUP BY 
            sess1.VehicleId
    ),
    AggregatedData AS (
        SELECT 
            vbd.DealerId,
            vbd.CustomerId,
            vbd.SiteId,
            vbd.DepartmentId,
            COUNT(DISTINCT vbd.VehicleId) AS VehiclesCount,
            SUM(vbd.IsInactiveTwentyFourHours) AS InactiveVehicleCountLastTwentyFourHours,
            SUM(vbd.IsInactiveSeventyTwoHours) AS InactiveVehicleCountLastSeventyTwoHours,
            SUM(CASE WHEN vbd.VORStatus = 1 THEN 1 ELSE 0 END) AS VehicleVORModeCount,
            SUM(ISNULL(imp.RedImpactCountToday, 0)) AS RedImpactCountToday,
            SUM(ISNULL(imp.RedImpactCountYesterday, 0)) AS RedImpactCountYesterday,
            AVG(ISNULL(imp.RedImpactDailyAverageCountLastWeek, 0)) AS RedImpactDailyAverageCountLastWeek,
            AVG(ISNULL(imp.RedImpactDailyAverageCountLastFourWeeks, 0)) AS RedImpactDailyAverageCountLastFourWeeks
        FROM 
            VehicleBaseData vbd
            LEFT JOIN ImpactStats imp ON vbd.VehicleId = imp.VehicleId
        GROUP BY 
            GROUPING SETS (
                (vbd.DealerId, vbd.CustomerId, vbd.SiteId, vbd.DepartmentId),
                (vbd.DealerId, vbd.CustomerId, vbd.SiteId),
                (vbd.DealerId, vbd.CustomerId),
                (vbd.SiteId, vbd.DepartmentId),
                (vbd.DealerId, vbd.CustomerId, vbd.DepartmentId),
                (vbd.SiteId),
                (vbd.DepartmentId),
                ()
            )
    )
    SELECT 
        NEWID() AS Id,
        ad.DealerId,
        ad.CustomerId,
        ad.SiteId,
        ad.DepartmentId,
        ad.VehiclesCount,
        ISNULL(SUM(os.OverlappingSessionCount), 0) AS SessionCountLastTwentyFourHours,
        ad.InactiveVehicleCountLastTwentyFourHours,
        ad.InactiveVehicleCountLastSeventyTwoHours,
        ad.VehicleVORModeCount,
        ad.RedImpactCountToday,
        ad.RedImpactCountYesterday,
        ad.RedImpactDailyAverageCountLastWeek,
        ad.RedImpactDailyAverageCountLastFourWeeks
    FROM 
        AggregatedData ad
        LEFT JOIN OverlappingSessions os ON 
            ad.DealerId IS NULL AND 
            ad.CustomerId IS NULL AND 
            ad.SiteId IS NULL AND 
            ad.DepartmentId IS NULL
    GROUP BY
        ad.DealerId,
        ad.CustomerId,
        ad.SiteId,
        ad.DepartmentId,
        ad.VehiclesCount,
        ad.InactiveVehicleCountLastTwentyFourHours,
        ad.InactiveVehicleCountLastSeventyTwoHours,
        ad.VehicleVORModeCount,
        ad.RedImpactCountToday,
        ad.RedImpactCountYesterday,
        ad.RedImpactDailyAverageCountLastWeek,
        ad.RedImpactDailyAverageCountLastFourWeeks;
END
GO 

-- Create GetDriverLicenseExpiry stored procedure
-- Fix GetDriverLicenseExpiry stored procedure to include timezone handling
CREATE OR ALTER PROCEDURE [dbo].[GetDriverLicenseExpiry]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @DealerId UNIQUEIDENTIFIER = NULL,
    @ReferenceDate DATETIME = NULL
AS
BEGIN
    SET NOCOUNT ON;

    SET @ReferenceDate = COALESCE(@ReferenceDate, GETUTCDATE());

    DECLARE @GroupingLevel varchar(50)
    SET @GroupingLevel = 
        CASE 
            WHEN @DepartmentId IS NOT NULL THEN 'department'
            WHEN @SiteId IS NOT NULL THEN 'site'
            WHEN @CustomerId IS NOT NULL THEN 'customer'
            WHEN @DealerId IS NOT NULL THEN 'dealer'
            ELSE 'total'
        END;

    WITH ValidLicenses AS (
        SELECT 
            p.Id as PersonId,
            p.CustomerId,
            p.SiteId,
            p.DepartmentId,
            c.DealerId,
            ld.ExpiryDate,
            CASE
                WHEN DATEADD(HOUR, tz.UTCOffset, ld.ExpiryDate) < @ReferenceDate THEN 'Overdue'
                WHEN DATEADD(HOUR, tz.UTCOffset, ld.ExpiryDate) >= @ReferenceDate AND DATEADD(HOUR, tz.UTCOffset, ld.ExpiryDate) < DATEADD(MONTH, 1, @ReferenceDate) THEN '< 1 month'
                WHEN DATEADD(HOUR, tz.UTCOffset, ld.ExpiryDate) > DATEADD(MONTH, 1, @ReferenceDate) AND DATEADD(HOUR, tz.UTCOffset, ld.ExpiryDate) <= DATEADD(MONTH, 3, @ReferenceDate) THEN '< 3 months'
            END as ExpiryStatus
        FROM dbo.Person p
        LEFT JOIN dbo.Customer c ON p.CustomerId = c.Id
        LEFT JOIN dbo.Dealer de ON c.DealerId = de.Id
        LEFT JOIN dbo.Department d ON p.DepartmentId = d.Id
        LEFT JOIN dbo.Site s ON p.SiteId = s.Id
        LEFT JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
        LEFT JOIN dbo.Driver dr ON p.DriverId = dr.Id
        LEFT JOIN dbo.LicenceDetail ld ON dr.LicenceDetailId = ld.Id
        WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
            AND (@SiteId IS NULL OR p.SiteId = @SiteId)
            AND (@DepartmentId IS NULL OR p.DepartmentId = @DepartmentId)
            AND (@DealerId IS NULL OR de.Id = @DealerId)
            AND ld.ExpiryDate IS NOT NULL
    )

    -- Less than 3 months expiry
    SELECT
        NEWID() AS Id,
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN DealerId
            ELSE NULL 
        END AS DealerId,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN CustomerId
            ELSE NULL 
        END AS CustomerId,
        CASE 
            WHEN @GroupingLevel = 'site' THEN SiteId
            ELSE NULL 
        END AS SiteId,
        CASE 
            WHEN @GroupingLevel = 'department' THEN DepartmentId
            ELSE NULL 
        END AS DepartmentId,
        '< 3 months' AS TimePeriod,
        COUNT(CASE WHEN ExpiryStatus = '< 3 months' THEN 1 END) AS ExpiredLicenseCount
    FROM ValidLicenses
    GROUP BY
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN DealerId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN CustomerId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'site' THEN SiteId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'department' THEN DepartmentId
            ELSE NULL 
        END

    UNION ALL

    -- Less than 1 month expiry
    SELECT
        NEWID() AS Id,
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN DealerId
            ELSE NULL 
        END AS DealerId,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN CustomerId
            ELSE NULL 
        END AS CustomerId,
        CASE 
            WHEN @GroupingLevel = 'site' THEN SiteId
            ELSE NULL 
        END AS SiteId,
        CASE 
            WHEN @GroupingLevel = 'department' THEN DepartmentId
            ELSE NULL 
        END AS DepartmentId,
        '< 1 month' AS TimePeriod,
        COUNT(CASE WHEN ExpiryStatus = '< 1 month' THEN 1 END) AS ExpiredLicenseCount
    FROM ValidLicenses
    GROUP BY
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN DealerId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN CustomerId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'site' THEN SiteId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'department' THEN DepartmentId
            ELSE NULL 
        END

    UNION ALL

    -- Overdue licenses
    SELECT
        NEWID() AS Id,
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN DealerId
            ELSE NULL 
        END AS DealerId,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN CustomerId
            ELSE NULL 
        END AS CustomerId,
        CASE 
            WHEN @GroupingLevel = 'site' THEN SiteId
            ELSE NULL 
        END AS SiteId,
        CASE 
            WHEN @GroupingLevel = 'department' THEN DepartmentId
            ELSE NULL 
        END AS DepartmentId,
        'Overdue' AS TimePeriod,
        COUNT(CASE WHEN ExpiryStatus = 'Overdue' THEN 1 END) AS ExpiredLicenseCount
    FROM ValidLicenses
    GROUP BY
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN DealerId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN CustomerId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'site' THEN SiteId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'department' THEN DepartmentId
            ELSE NULL 
        END;
END
GO 

-- Create GetTodaysPreopCheck stored procedure
-- Fix GetTodaysPreopCheck stored procedure to include timezone handling
CREATE OR ALTER PROCEDURE [dbo].[GetTodaysPreopCheck]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @DealerId UNIQUEIDENTIFIER = NULL,
    @ReferenceDate DATETIME = NULL
AS
BEGIN
    SET NOCOUNT ON;

    -- If no reference date provided, use current UTC time
    SET @ReferenceDate = COALESCE(@ReferenceDate, GETUTCDATE());
    
    -- Get the start of the day in UTC for the reference date
    DECLARE @StartOfDay DATETIME = CAST(CAST(@ReferenceDate AS DATE) AS DATETIME);
    
    DECLARE @GroupingLevel varchar(50)
    SET @GroupingLevel = 
        CASE 
            WHEN @DealerId IS NOT NULL THEN 'dealer'
            WHEN @CustomerId IS NOT NULL THEN 'customer'
            WHEN @SiteId IS NOT NULL THEN 'site'
            WHEN @DepartmentId IS NOT NULL THEN 'department'
            ELSE 'total'
        END;

    WITH Counts AS (
        SELECT
            NEWID() AS Id,
            CASE 
                WHEN @GroupingLevel = 'dealer' THEN de.Id
                ELSE NULL 
            END AS DealerId,
            CASE 
                WHEN @GroupingLevel = 'customer' THEN c.Id
                ELSE NULL 
            END AS CustomerId,
            CASE 
                WHEN @GroupingLevel = 'site' THEN s.Id
                ELSE NULL 
            END AS SiteId,
            CASE 
                WHEN @GroupingLevel = 'department' THEN d.Id
                ELSE NULL 
            END AS DepartmentId,
            COUNT(CASE 
                WHEN DATEADD(HOUR, tz.UTCOffset, ckl.StartTime) >= @StartOfDay 
                AND DATEADD(HOUR, tz.UTCOffset, ckl.StartTime) <= @ReferenceDate 
                AND ckl.EndTime IS NOT NULL THEN 1 
                ELSE NULL 
            END) AS Status0Count,
            COUNT(CASE 
                WHEN DATEADD(HOUR, tz.UTCOffset, ckl.StartTime) >= @StartOfDay 
                AND DATEADD(HOUR, tz.UTCOffset, ckl.StartTime) <= @ReferenceDate 
                AND ckl.EndTime IS NULL THEN 1 
                ELSE NULL 
            END) AS Status1Count
        FROM 
            dbo.Vehicle v
            LEFT JOIN dbo.Department d ON d.Id = v.DepartmentId
            LEFT JOIN dbo.Site s ON s.Id = d.SiteId			
            LEFT JOIN dbo.Customer c ON c.Id = s.CustomerId
            LEFT JOIN dbo.Dealer de ON de.id = c.DealerId
            LEFT JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
            LEFT JOIN dbo.Session sess ON sess.VehicleId = v.Id
            LEFT JOIN dbo.ChecklistResult ckl ON sess.Id = ckl.SessionId1
        WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
            AND (@SiteId IS NULL OR s.Id = @SiteId)
            AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
            AND (@DealerId IS NULL OR de.Id = @DealerId)
        GROUP BY
            CASE 
                WHEN @GroupingLevel = 'dealer' THEN de.Id
                ELSE NULL 
            END,
            CASE 
                WHEN @GroupingLevel = 'customer' THEN c.Id
                ELSE NULL 
            END,
            CASE 
                WHEN @GroupingLevel = 'site' THEN s.Id
                ELSE NULL 
            END,
            CASE 
                WHEN @GroupingLevel = 'department' THEN d.Id
                ELSE NULL 
            END
    )

    -- First result set (Status 0)
    SELECT
        Id,
        DealerId,
        CustomerId,
        SiteId,
        DepartmentId,
        0 AS Status,
        ROUND(
            CASE
                WHEN COALESCE(Status0Count + Status1Count, 0) = 0 THEN 0
                ELSE CAST(Status0Count AS DECIMAL(18, 4)) / COALESCE(Status0Count + Status1Count, 1) * 100
            END,
            2
        ) AS Percentage
    FROM Counts

    UNION ALL

    -- Second result set (Status 1)
    SELECT
        Id,
        DealerId,
        CustomerId,
        SiteId,
        DepartmentId,
        1 AS Status,
        ROUND(
            CASE
                WHEN COALESCE(Status0Count + Status1Count, 0) = 0 THEN 0
                ELSE CAST(Status1Count AS DECIMAL(18, 4)) / COALESCE(Status0Count + Status1Count, 1) * 100
            END,
            2
        ) AS Percentage
    FROM Counts

    UNION ALL

    -- Third result set (Status 2 - Critical checks)
    SELECT
        NEWID() AS Id,
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN de.Id
            ELSE NULL 
        END AS DealerId,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN c.Id
            ELSE NULL 
        END AS CustomerId,
        CASE 
            WHEN @GroupingLevel = 'site' THEN s.Id
            ELSE NULL 
        END AS SiteId,
        CASE 
            WHEN @GroupingLevel = 'department' THEN d.Id
            ELSE NULL 
        END AS DepartmentId,
        2 AS Status,
        CASE WHEN 
            EXISTS (
                SELECT 1
                FROM [dbo].[ChecklistDetail] cd
                LEFT JOIN [dbo].[PreOperationalChecklist] poc ON cd.[PreOperationalChecklistId] = poc.[Id] and poc.[Critical] = 1 and cd.[Answer] <> poc.[ExpectedAnswer]
                LEFT JOIN dbo.ChecklistResult ckl ON cd.[ChecklistResultId] = ckl.[Id]
                LEFT JOIN dbo.Session sess ON ckl.SessionId1 = sess.Id
                LEFT JOIN dbo.Vehicle v ON sess.VehicleId = v.Id
                LEFT JOIN dbo.Department d2 ON d2.Id = v.DepartmentId
                LEFT JOIN dbo.Site s2 ON s2.Id = d2.SiteId
                LEFT JOIN dbo.Timezone tz2 ON s2.TimezoneId = tz2.Id
                WHERE DATEADD(HOUR, tz2.UTCOffset, ckl.StartTime) >= @StartOfDay 
                AND DATEADD(HOUR, tz2.UTCOffset, ckl.StartTime) <= @ReferenceDate
                AND (@CustomerId IS NULL OR s2.CustomerId = @CustomerId)
                AND (@SiteId IS NULL OR s2.Id = @SiteId)
                AND (@DepartmentId IS NULL OR d2.Id = @DepartmentId)
                AND (@DealerId IS NULL OR s2.CustomerId IN (SELECT Id FROM dbo.Customer WHERE DealerId = @DealerId))
            ) THEN 
                ROUND(
                    CASE
                        WHEN COALESCE(COUNT(*), 0) = 0 THEN 0
                        ELSE CAST(COUNT(*) AS DECIMAL(18, 4)) / COALESCE(COUNT(*), 1) * 100
                    END,
                    2
                ) 
            ELSE 0
        END AS Percentage
    FROM      
        dbo.Vehicle v
        LEFT JOIN dbo.Department d ON d.Id = v.DepartmentId
        LEFT JOIN dbo.Site s ON s.Id = d.SiteId			
        LEFT JOIN dbo.Customer c ON c.Id = s.CustomerId
        LEFT JOIN dbo.Dealer de ON de.id = c.DealerId
        LEFT JOIN dbo.Session sess ON sess.VehicleId = v.Id
        LEFT JOIN dbo.ChecklistResult ckl ON sess.Id = ckl.SessionId1
    WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
        AND (@SiteId IS NULL OR s.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
        AND (@DealerId IS NULL OR de.Id = @DealerId)
    GROUP BY
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN de.Id
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN c.Id
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'site' THEN s.Id
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'department' THEN d.Id
            ELSE NULL 
        END;
END;
GO 

-- Create GetTodaysImpact stored procedure
-- Fix GetTodaysImpact stored procedure to include timezone handling
CREATE OR ALTER PROCEDURE [dbo].[GetTodaysImpact]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @DealerId UNIQUEIDENTIFIER = NULL,
    @ReferenceDate DATETIME = NULL
AS
BEGIN
    SET NOCOUNT ON;

    -- If no reference date provided, use current UTC time
    SET @ReferenceDate = COALESCE(@ReferenceDate, GETUTCDATE());
    
    -- Get the start of the day in UTC for the reference date
    DECLARE @StartOfDay DATETIME = CAST(CAST(@ReferenceDate AS DATE) AS DATETIME);

    DECLARE @GroupingLevel varchar(50)
    SET @GroupingLevel = 
        CASE 
            WHEN @DealerId IS NOT NULL THEN 'dealer'
            WHEN @CustomerId IS NOT NULL THEN 'customer'
            WHEN @SiteId IS NOT NULL THEN 'site'
            WHEN @DepartmentId IS NOT NULL THEN 'department'
            ELSE 'total'
        END;

    WITH ValidVehicles AS (
        SELECT DISTINCT v.Id AS VehicleId, d.Id AS DepartmentId, s.Id AS SiteId, c.Id AS CustomerId, de.Id AS DealerId, tz.UTCOffset
        FROM dbo.Vehicle v
        LEFT JOIN dbo.Department d ON d.Id = v.DepartmentId
        LEFT JOIN dbo.Site s ON s.Id = d.SiteId
        LEFT JOIN dbo.Customer c ON c.Id = s.CustomerId
        LEFT JOIN dbo.Dealer de ON c.DealerId = de.Id
        LEFT JOIN dbo.Timezone tz ON tz.id = s.TimezoneId
        LEFT JOIN dbo.Session sess ON sess.VehicleId = v.Id
        INNER JOIN dbo.Impact imp ON imp.SessionId = sess.Id 
            AND imp.Threshold != 0 
            AND DATEADD(HOUR, tz.UTCOffset, imp.ImpactDateTime) >= @StartOfDay
            AND DATEADD(HOUR, tz.UTCOffset, imp.ImpactDateTime) <= @ReferenceDate
        WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
            AND (@SiteId IS NULL OR s.Id = @SiteId)
            AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
            AND (@DealerId IS NULL OR de.Id = @DealerId)
    )

    -- Query for Amber impact level
    SELECT 
        NEWID() AS [Id],
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN vv.DealerId
            ELSE NULL 
        END AS DealerId,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN vv.CustomerId
            ELSE NULL 
        END AS CustomerId,
        CASE 
            WHEN @GroupingLevel = 'site' THEN vv.SiteId
            ELSE NULL 
        END AS SiteId,
        CASE 
            WHEN @GroupingLevel = 'department' THEN vv.DepartmentId
            ELSE NULL 
        END AS DepartmentId,
        0 AS ImpactType,
        'Amber' AS ImpactLevel,
        COUNT(CASE 
            WHEN imp.ShockValue >= imp.Threshold * 5 AND imp.ShockValue < imp.Threshold * 10 THEN 1 
            ELSE NULL 
        END) AS NumberOfImpacts
    FROM ValidVehicles vv
    INNER JOIN dbo.Session sess ON sess.VehicleId = vv.VehicleId
    INNER JOIN dbo.Impact imp ON imp.SessionId = sess.Id 
        AND imp.Threshold != 0 
        AND DATEADD(HOUR, vv.UTCOffset, imp.ImpactDateTime) >= @StartOfDay
        AND DATEADD(HOUR, vv.UTCOffset, imp.ImpactDateTime) <= @ReferenceDate
    GROUP BY
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN vv.DealerId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN vv.CustomerId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'site' THEN vv.SiteId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'department' THEN vv.DepartmentId
            ELSE NULL 
        END

    UNION ALL

    -- Query for Red impact level
    SELECT 
        NEWID() AS [Id],
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN vv.DealerId
            ELSE NULL 
        END AS DealerId,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN vv.CustomerId
            ELSE NULL 
        END AS CustomerId,
        CASE 
            WHEN @GroupingLevel = 'site' THEN vv.SiteId
            ELSE NULL 
        END AS SiteId,
        CASE 
            WHEN @GroupingLevel = 'department' THEN vv.DepartmentId
            ELSE NULL 
        END AS DepartmentId,
        1 AS ImpactType,
        'Red' AS ImpactLevel,
        COUNT(CASE 
            WHEN imp.ShockValue > imp.Threshold * 10 THEN 1 
            ELSE NULL 
        END) AS NumberOfImpacts
    FROM ValidVehicles vv
    INNER JOIN dbo.Session sess ON sess.VehicleId = vv.VehicleId
    INNER JOIN dbo.Impact imp ON imp.SessionId = sess.Id 
        AND imp.Threshold != 0 
        AND DATEADD(HOUR, vv.UTCOffset, imp.ImpactDateTime) >= @StartOfDay
        AND DATEADD(HOUR, vv.UTCOffset, imp.ImpactDateTime) <= @ReferenceDate
    GROUP BY
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN vv.DealerId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN vv.CustomerId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'site' THEN vv.SiteId
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'department' THEN vv.DepartmentId
            ELSE NULL 
        END;
END;
GO 

-- Create GetVehicleUtilizationLastTwelveHours stored procedure
-- Fix GetVehicleUtilizationLastTwelveHours stored procedure to include timezone handling
CREATE OR ALTER PROCEDURE [dbo].[GetVehicleUtilizationLastTwelveHours]
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @SiteId UNIQUEIDENTIFIER = NULL,
    @DepartmentId UNIQUEIDENTIFIER = NULL,
    @DealerId UNIQUEIDENTIFIER = NULL,
    @ReferenceDate DATETIME = NULL
AS
BEGIN
    SET NOCOUNT ON;

    -- Use provided reference date if available, otherwise use current time
    SET @ReferenceDate = COALESCE(@ReferenceDate, GETDATE());

    DECLARE @GroupingLevel varchar(50)
    SET @GroupingLevel = 
        CASE 
            WHEN @DealerId IS NOT NULL THEN 'dealer'
            WHEN @CustomerId IS NOT NULL THEN 'customer'
            WHEN @SiteId IS NOT NULL THEN 'site'
            WHEN @DepartmentId IS NOT NULL THEN 'department'
            ELSE 'total'
        END;

    WITH HourSeries AS (
        SELECT DATEADD(HOUR, -11, COALESCE(@ReferenceDate, GETDATE())) AS HourStart
        UNION ALL
        SELECT DATEADD(HOUR, 1, HourStart)
        FROM HourSeries
        WHERE HourStart < COALESCE(@ReferenceDate, GETDATE())
    )
    SELECT 
        NEWID() AS Id,
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN de.Id
            ELSE NULL 
        END AS DealerId,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN c.Id
            ELSE NULL 
        END AS CustomerId,
        CASE 
            WHEN @GroupingLevel = 'site' THEN s.Id
            ELSE NULL 
        END AS SiteId,
        CASE 
            WHEN @GroupingLevel = 'department' THEN d.Id
            ELSE NULL 
        END AS DepartmentId,
        CAST(FORMAT(HourStart, N'HH\:00') AS VARCHAR(5)) AS TimePeriod,
        COUNT(DISTINCT sess.Id) AS NumberOfSessions
    FROM HourSeries hrs
    CROSS JOIN dbo.Vehicle v
    INNER JOIN dbo.Department d ON v.DepartmentId = d.Id
    INNER JOIN dbo.Site s ON d.SiteId = s.Id
    INNER JOIN dbo.Customer c ON s.CustomerId = c.Id
    INNER JOIN dbo.Dealer de ON c.DealerId = de.Id
    LEFT JOIN dbo.Timezone tz ON s.TimezoneId = tz.Id
    LEFT JOIN dbo.Session sess ON sess.VehicleId = v.Id 
        AND DATEADD(HOUR, tz.UTCOffset, sess.StartTime) <= DATEADD(HOUR, 1, hrs.HourStart)
        AND DATEADD(HOUR, tz.UTCOffset, sess.StartTime) >= hrs.HourStart
    WHERE (@CustomerId IS NULL OR c.Id = @CustomerId)
        AND (@SiteId IS NULL OR s.Id = @SiteId)
        AND (@DepartmentId IS NULL OR d.Id = @DepartmentId)
        AND (@DealerId IS NULL OR de.Id = @DealerId)
    GROUP BY 
        CASE 
            WHEN @GroupingLevel = 'dealer' THEN de.Id
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'customer' THEN c.Id
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'site' THEN s.Id
            ELSE NULL 
        END,
        CASE 
            WHEN @GroupingLevel = 'department' THEN d.Id
            ELSE NULL 
        END,
        HourStart
    ORDER BY HourStart;
END
GO 