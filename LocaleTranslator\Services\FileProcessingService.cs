using LocaleTranslator.Models;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace LocaleTranslator.Services;

public class FileProcessingService
{
    private readonly ClaudeTranslationService? _translationService;
    private readonly ILogger<FileProcessingService> _logger;

    public FileProcessingService(ClaudeTranslationService? translationService, ILogger<FileProcessingService> logger)
    {
        _translationService = translationService;
        _logger = logger;
    }

    public async Task<List<string>> DiscoverJsonFilesAsync(string sourceLocalesPath)
    {
        if (!Directory.Exists(sourceLocalesPath))
        {
            throw new DirectoryNotFoundException($"Source locales directory not found: {sourceLocalesPath}");
        }

        var jsonFiles = Directory.GetFiles(sourceLocalesPath, "*.json", SearchOption.AllDirectories)
            .Where(file => !IsBackupOrTempFile(file))
            .OrderBy(file => file)
            .ToList();

        _logger.LogInformation("Discovered {FileCount} JSON files in {SourcePath}", jsonFiles.Count, sourceLocalesPath);

        foreach (var file in jsonFiles)
        {
            var relativePath = Path.GetRelativePath(sourceLocalesPath, file);
            _logger.LogDebug("Found: {RelativePath}", relativePath);
        }

        return jsonFiles;
    }

    public async Task<ProcessingResults> ProcessFilesAsync(TranslationOptions options)
    {
        var results = new ProcessingResults();
        var sourceFiles = await DiscoverJsonFilesAsync(options.SourceLocalesPath);

        if (!sourceFiles.Any())
        {
            _logger.LogWarning("No JSON files found to translate");
            return results;
        }

        _logger.LogInformation("Starting translation of {FileCount} files to {TargetLanguage}",
            sourceFiles.Count, options.TargetLanguage);

        // Create target directory structure
        if (!options.DryRun)
        {
            Directory.CreateDirectory(options.TargetLocalesPath);
        }

        // Process files with concurrency control
        var semaphore = new SemaphoreSlim(options.MaxConcurrency, options.MaxConcurrency);
        var tasks = sourceFiles.Select(async sourceFile =>
        {
            await semaphore.WaitAsync();
            try
            {
                await Task.Delay(options.DelayBetweenRequests);
                return await ProcessSingleFileAsync(sourceFile, options);
            }
            finally
            {
                semaphore.Release();
            }
        });

        var fileResults = await Task.WhenAll(tasks);

        foreach (var result in fileResults)
        {
            results.AddResult(result);
        }

        LogResults(results);
        return results;
    }

    public async Task<int> ExportTranslationsToCsvAsync(TranslationOptions options, string outputCsvPath, CsvExportOptions csvOptions)
    {
        var entries = new List<TranslationEntry>();
        var sourceLanguage = Path.GetFileName(options.SourceLocalesPath).Trim();
        if (string.IsNullOrWhiteSpace(sourceLanguage))
        {
            sourceLanguage = "source";
        }

        var sourceFiles = await DiscoverJsonFilesAsync(options.SourceLocalesPath);
        foreach (var sourceFile in sourceFiles)
        {
            var relativePath = Path.GetRelativePath(options.SourceLocalesPath, sourceFile);
            var targetFile = Path.Combine(options.TargetLocalesPath, relativePath);
            if (!File.Exists(targetFile))
            {
                continue;
            }

            string sourceJson;
            string targetJson;
            try
            {
                sourceJson = await File.ReadAllTextAsync(sourceFile);
                targetJson = await File.ReadAllTextAsync(targetFile);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed reading files for CSV export: {RelativePath}", relativePath);
                continue;
            }

            try
            {
                var sourceToken = JToken.Parse(sourceJson);
                var targetToken = JToken.Parse(targetJson);
                CollectTranslationEntries(sourceToken, targetToken, string.Empty, relativePath, entries, sourceLanguage, options.TargetLanguage);
            }
            catch (JsonException ex)
            {
                _logger.LogWarning(ex, "Invalid JSON while preparing CSV export: {RelativePath}", relativePath);
                continue;
            }
        }

        Directory.CreateDirectory(Path.GetDirectoryName(outputCsvPath) ?? ".");
        await WriteEntriesToCsvAsync(entries, outputCsvPath, csvOptions);
        _logger.LogInformation("CSV export created: {CsvPath} ({Count} entries)", outputCsvPath, entries.Count);
        return entries.Count;
    }

    private void CollectTranslationEntries(JToken source, JToken? target, string prefix, string relativePath, List<TranslationEntry> entries, string sourceLanguage, string targetLanguage)
    {
        if (source is JObject sourceObj)
        {
            var targetObj = target as JObject;
            foreach (var prop in sourceObj.Properties())
            {
                var key = string.IsNullOrEmpty(prefix) ? prop.Name : $"{prefix}.{prop.Name}";
                var targetChild = targetObj != null ? targetObj.Property(prop.Name)?.Value : null;
                CollectTranslationEntries(prop.Value, targetChild, key, relativePath, entries, sourceLanguage, targetLanguage);
            }
            return;
        }

        if (source is JArray sourceArr)
        {
            var targetArr = target as JArray;
            for (int i = 0; i < sourceArr.Count; i++)
            {
                var key = $"{prefix}[{i}]";
                var targetItem = targetArr != null && i < targetArr.Count ? targetArr[i] : null;
                CollectTranslationEntries(sourceArr[i], targetItem, key, relativePath, entries, sourceLanguage, targetLanguage);
            }
            return;
        }

        if (source.Type == JTokenType.String)
        {
            var sourceText = source.Value<string>() ?? string.Empty;
            var translatedText = target?.Type == JTokenType.String ? (target?.Value<string>() ?? string.Empty) : string.Empty;
            entries.Add(new TranslationEntry
            {
                FilePath = relativePath.Replace('\\', '/'),
                KeyPath = prefix,
                SourceLanguage = sourceLanguage,
                TargetLanguage = targetLanguage,
                SourceText = sourceText,
                TranslatedText = translatedText
            });
        }
    }

    private async Task WriteEntriesToCsvAsync(List<TranslationEntry> entries, string outputCsvPath, CsvExportOptions options)
    {
        var sb = new StringBuilder();
        var d = string.IsNullOrEmpty(options.Delimiter) ? "," : options.Delimiter;

        void AppendCsv(string? value)
        {
            var v = value ?? string.Empty;
            var needQuotes = v.Contains('"') || v.Contains(',') || v.Contains('\n') || v.Contains('\r') || v.Contains('\t') || v.StartsWith(' ') || v.EndsWith(' ');
            v = v.Replace("\"", "\"\"");
            sb.Append(needQuotes ? $"\"{v}\"" : v);
        }

        AppendCsv("SourceText"); sb.Append(d);
        AppendCsv("TranslatedText"); sb.Append(d);
        AppendCsv("SourceLanguage"); sb.Append(d);
        AppendCsv("TargetLanguage"); sb.Append(d);
        AppendCsv("FilePath"); sb.Append(d);
        AppendCsv("KeyPath"); sb.AppendLine();

        foreach (var e in entries)
        {
            AppendCsv(e.SourceText); sb.Append(d);
            AppendCsv(e.TranslatedText); sb.Append(d);
            AppendCsv(e.SourceLanguage); sb.Append(d);
            AppendCsv(e.TargetLanguage); sb.Append(d);
            AppendCsv(e.FilePath); sb.Append(d);
            AppendCsv(e.KeyPath); sb.AppendLine();
        }

        var useUtf8Bom = options.Encoding is UTF8Encoding u && u.GetPreamble().Length > 0;
        var encoding = options.Encoding == Encoding.UTF8 && !useUtf8Bom ? new UTF8Encoding(true) : options.Encoding;
        await File.WriteAllTextAsync(outputCsvPath, sb.ToString(), encoding);
    }

    private async Task<FileProcessingResult> ProcessSingleFileAsync(string sourceFile, TranslationOptions options)
    {
        var result = new FileProcessingResult
        {
            SourceFile = sourceFile,
            RelativePath = Path.GetRelativePath(options.SourceLocalesPath, sourceFile)
        };

        try
        {
            // Read source file
            var jsonContent = await File.ReadAllTextAsync(sourceFile);

            // Validate JSON
            try
            {
                JsonConvert.DeserializeObject(jsonContent);
            }
            catch (JsonException ex)
            {
                result.Success = false;
                result.ErrorMessage = $"Invalid JSON in source file: {ex.Message}";
                _logger.LogError("Invalid JSON in {SourceFile}: {Error}", sourceFile, ex.Message);
                return result;
            }

            // Determine target file path
            var targetFile = Path.Combine(options.TargetLocalesPath, result.RelativePath);
            result.TargetFile = targetFile;

            // Check if target exists
            if (File.Exists(targetFile))
            {
                // Check for key differences between source and target
                var keyDifference = await CompareFileKeysAsync(sourceFile, targetFile, result.RelativePath);

                if (keyDifference.HasDifferences)
                {
                    _logger.LogInformation("Key differences found in {RelativePath}. Missing keys: {MissingCount}, Extra keys: {ExtraCount}. Rerunning translation.",
                        result.RelativePath, keyDifference.MissingKeysCount, keyDifference.ExtraKeysCount);

                    if (options.DryRun)
                    {
                        result.Success = true;
                        result.DryRun = true;
                        result.RetranslatedDueToKeyDifferences = true;
                        _logger.LogInformation("DRY RUN: Would retranslate {RelativePath} due to key differences", result.RelativePath);
                        return result;
                    }

                    // Continue with translation to update the file
                    result.RetranslatedDueToKeyDifferences = true;
                    _logger.LogDebug("Proceeding with translation for {SourceFile} due to key differences", sourceFile);
                }
                else
                {
                    result.Success = true;
                    result.Skipped = true;
                    result.SkipReason = "Target file exists with matching keys";
                    _logger.LogDebug("Skipping {SourceFile} - target exists with matching keys", sourceFile);
                    return result;
                }
            }

            if (options.DryRun)
            {
                result.Success = true;
                result.DryRun = true;
                _logger.LogInformation("DRY RUN: Would translate {RelativePath}", result.RelativePath);
                return result;
            }

            // Create target directory
            var targetDir = Path.GetDirectoryName(targetFile);
            if (!string.IsNullOrEmpty(targetDir))
            {
                Directory.CreateDirectory(targetDir);
            }

            return await ProcessNormalFileAsync(sourceFile, targetFile, jsonContent, options, result);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Unexpected error processing {SourceFile}", sourceFile);
            return result;
        }
    }

    private async Task<FileProcessingResult> ProcessNormalFileAsync(string sourceFile, string targetFile, string jsonContent, TranslationOptions options, FileProcessingResult result)
    {
        _logger.LogDebug("Processing normal file: {SourceFile} -> {TargetFile}", sourceFile, targetFile);
        _logger.LogDebug("Source JSON content length: {ContentLength} characters", jsonContent.Length);

        // Prepare translation request
        var translationRequest = new TranslationRequest
        {
            SourcePath = sourceFile,
            TargetPath = targetFile,
            TargetLanguage = options.TargetLanguage,
            JsonContent = jsonContent
        };

        // Translate
        if (_translationService == null)
        {
            result.Success = false;
            result.ErrorMessage = "Translation service is not available";
            _logger.LogError("Translation service is not available for {SourceFile}", sourceFile);
            return result;
        }

        _logger.LogDebug("Calling translation service for {SourceFile}", sourceFile);
        var translationResult = await _translationService.TranslateJsonAsync(translationRequest);

        if (!translationResult.Success)
        {
            result.Success = false;
            result.ErrorMessage = translationResult.ErrorMessage;
            _logger.LogError("Translation failed for {SourceFile}: {Error}", sourceFile, translationResult.ErrorMessage);
            return result;
        }

        _logger.LogDebug("Translation successful for {SourceFile}. Translated content length: {TranslatedLength}",
            sourceFile, translationResult.TranslatedContent.Length);
        _logger.LogDebug("Translated content (first 200 chars): {TranslatedPreview}",
            translationResult.TranslatedContent.Length > 200 ? translationResult.TranslatedContent.Substring(0, 200) + "..." : translationResult.TranslatedContent);

        // Validate translated JSON
        try
        {
            _logger.LogDebug("Validating translated JSON for {SourceFile}", sourceFile);
            JsonConvert.DeserializeObject(translationResult.TranslatedContent);
            _logger.LogDebug("JSON validation successful for {SourceFile}", sourceFile);
        }
        catch (JsonException ex)
        {
            result.Success = false;
            result.ErrorMessage = $"Invalid JSON returned from translation: {ex.Message}";
            _logger.LogError("Invalid translated JSON for {SourceFile}: {Error}", sourceFile, ex.Message);
            _logger.LogError("Failed translated content (first 1000 chars): {FailedContent}",
                translationResult.TranslatedContent.Length > 1000 ? translationResult.TranslatedContent.Substring(0, 1000) + "..." : translationResult.TranslatedContent);
            return result;
        }

        // Write translated file
        await File.WriteAllTextAsync(targetFile, translationResult.TranslatedContent);

        result.Success = true;
        result.InputTokens = translationResult.InputTokens;
        result.OutputTokens = translationResult.OutputTokens;

        _logger.LogInformation("Successfully translated {RelativePath} ({InputTokens}→{OutputTokens} tokens)",
            result.RelativePath, result.InputTokens, result.OutputTokens);

        return result;
    }

    public async Task<TranslationStatus> CheckTranslationStatusAsync(TranslationOptions options)
    {
        var status = new TranslationStatus();

        // Get all JSON files from source directory
        var sourceFiles = await DiscoverJsonFilesAsync(options.SourceLocalesPath);
        status.TotalSourceFiles = sourceFiles.Count;

        // Get relative paths for comparison
        var sourceRelativePaths = sourceFiles
            .Select(file => Path.GetRelativePath(options.SourceLocalesPath, file))
            .ToHashSet();

        // Check if target directory exists
        if (!Directory.Exists(options.TargetLocalesPath))
        {
            status.MissingFiles = sourceRelativePaths.ToList();
            status.TotalTargetFiles = 0;
            return status;
        }

        // Get all JSON files from target directory
        var targetFiles = Directory.GetFiles(options.TargetLocalesPath, "*.json", SearchOption.AllDirectories)
            .Where(file => !IsBackupOrTempFile(file))
            .ToList();

        status.TotalTargetFiles = targetFiles.Count;

        // Get relative paths for target files
        var targetRelativePaths = targetFiles
            .Select(file => Path.GetRelativePath(options.TargetLocalesPath, file))
            .ToHashSet();

        // Find missing files (in source but not in target)
        status.MissingFiles = sourceRelativePaths
            .Where(path => !targetRelativePaths.Contains(path))
            .OrderBy(path => path)
            .ToList();

        // Find extra files (in target but not in source)
        status.ExtraFiles = targetRelativePaths
            .Where(path => !sourceRelativePaths.Contains(path))
            .OrderBy(path => path)
            .ToList();

        // Check for key differences in files that exist in both source and target
        var commonFiles = sourceRelativePaths
            .Where(path => targetRelativePaths.Contains(path))
            .ToList();

        foreach (var relativePath in commonFiles)
        {
            var sourceFile = Path.Combine(options.SourceLocalesPath, relativePath);
            var targetFile = Path.Combine(options.TargetLocalesPath, relativePath);

            var keyDifference = await CompareFileKeysAsync(sourceFile, targetFile, relativePath);
            if (keyDifference.HasDifferences)
            {
                status.FilesWithKeyDifferences.Add(keyDifference);
                status.TotalMissingKeys += keyDifference.MissingKeysCount;
                status.TotalExtraKeys += keyDifference.ExtraKeysCount;
            }
        }

        return status;
    }

    public async Task<FileKeyDifference> CompareFileKeysAsync(string sourceFile, string targetFile, string relativePath)
    {
        var difference = new FileKeyDifference
        {
            FilePath = relativePath
        };

        try
        {
            // Read and parse source file
            var sourceContent = await File.ReadAllTextAsync(sourceFile);
            var sourceKeys = ExtractJsonKeys(sourceContent);

            // Read and parse target file
            var targetContent = await File.ReadAllTextAsync(targetFile);
            var targetKeys = ExtractJsonKeys(targetContent);

            // Debug logging for key comparison
            _logger.LogDebug("Key comparison for {FilePath}:", relativePath);
            _logger.LogDebug("Source keys count: {SourceCount}, Target keys count: {TargetCount}",
                sourceKeys.Count, targetKeys.Count);

            // Find missing keys (in source but not in target)
            difference.MissingKeys = sourceKeys
                .Where(key => !targetKeys.Contains(key))
                .OrderBy(key => key)
                .ToList();

            // Find extra keys (in target but not in source)
            difference.ExtraKeys = targetKeys
                .Where(key => !sourceKeys.Contains(key))
                .OrderBy(key => key)
                .ToList();

            // Debug logging for differences
            if (difference.HasDifferences)
            {
                _logger.LogDebug("Key differences found in {FilePath}:", relativePath);
                if (difference.MissingKeys.Count > 0)
                {
                    _logger.LogDebug("Missing keys: {MissingKeys}", string.Join(", ", difference.MissingKeys));
                }
                if (difference.ExtraKeys.Count > 0)
                {
                    _logger.LogDebug("Extra keys: {ExtraKeys}", string.Join(", ", difference.ExtraKeys));
                }
            }
            else
            {
                _logger.LogDebug("No key differences found in {FilePath}", relativePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to compare keys for file {FilePath}", relativePath);
            // If we can't compare, assume there are differences
            difference.MissingKeys.Add("ERROR: Could not parse file");
        }

        return difference;
    }

    private List<string> ExtractJsonKeys(string jsonContent)
    {
        var keys = new List<string>();
        try
        {
            var token = JToken.Parse(jsonContent);
            ExtractKeysRecursively(token, "", keys);
        }
        catch (JsonException ex)
        {
            _logger.LogDebug("Failed to parse JSON: {Error}", ex.Message);
            keys.Add("[invalid-json]");
        }
        return keys;
    }

    private void ExtractKeysRecursively(JToken token, string prefix, List<string> keys)
    {
        if (token is JObject obj)
        {
            foreach (var property in obj.Properties())
            {
                var currentKey = string.IsNullOrEmpty(prefix) ? property.Name : $"{prefix}.{property.Name}";
                keys.Add(currentKey);
                ExtractKeysRecursively(property.Value, currentKey, keys);
            }
        }
        else if (token is JArray arr)
        {
            var currentKey = string.IsNullOrEmpty(prefix) ? "[array]" : $"{prefix}[array]";
            if (!keys.Contains(currentKey))
            {
                keys.Add(currentKey);
            }
            foreach (var item in arr)
            {
                ExtractKeysRecursively(item, currentKey, keys);
            }
        }
        // For primitive values, do nothing
    }

    private bool IsBackupOrTempFile(string filePath)
    {
        var fileName = Path.GetFileName(filePath).ToLowerInvariant();
        return fileName.Contains(".bak") ||
               fileName.Contains(".tmp") ||
               fileName.Contains(".backup") ||
               fileName.StartsWith("~") ||
               fileName.EndsWith("~");
    }

    private void LogResults(ProcessingResults results)
    {
        _logger.LogInformation("""
            Translation Summary:
            - Total Files: {TotalFiles}
            - Successful: {SuccessfulFiles}
            - Failed: {FailedFiles}
            - Skipped: {SkippedFiles}
            - Retranslated due to key differences: {RetranslatedFiles}
            - Total Input Tokens: {TotalInputTokens:N0}
            - Total Output Tokens: {TotalOutputTokens:N0}
            """,
            results.TotalFiles,
            results.SuccessfulFiles,
            results.FailedFiles,
            results.SkippedFiles,
            results.RetranslatedFiles,
            results.TotalInputTokens,
            results.TotalOutputTokens);

        if (results.FailedFiles > 0)
        {
            _logger.LogWarning("Failed files:");
            foreach (var failure in results.Results.Where(r => !r.Success && !r.Skipped))
            {
                _logger.LogWarning("  {RelativePath}: {Error}", failure.RelativePath, failure.ErrorMessage);
            }
        }

        if (results.RetranslatedFiles > 0)
        {
            _logger.LogInformation("Files retranslated due to key differences:");
            foreach (var retranslated in results.Results.Where(r => r.RetranslatedDueToKeyDifferences))
            {
                _logger.LogInformation("  {RelativePath}", retranslated.RelativePath);
            }
        }
    }
}

public class ProcessingResults
{
    public List<FileProcessingResult> Results { get; } = new();

    public int TotalFiles => Results.Count;
    public int SuccessfulFiles => Results.Count(r => r.Success && !r.Skipped);
    public int FailedFiles => Results.Count(r => !r.Success);
    public int SkippedFiles => Results.Count(r => r.Skipped);
    public int RetranslatedFiles => Results.Count(r => r.RetranslatedDueToKeyDifferences);
    public int TotalInputTokens => Results.Sum(r => r.InputTokens);
    public int TotalOutputTokens => Results.Sum(r => r.OutputTokens);

    public void AddResult(FileProcessingResult result)
    {
        Results.Add(result);
    }
}

public class FileProcessingResult
{
    public string SourceFile { get; set; } = string.Empty;
    public string TargetFile { get; set; } = string.Empty;
    public string RelativePath { get; set; } = string.Empty;
    public bool Success { get; set; }
    public bool Skipped { get; set; }
    public bool DryRun { get; set; }
    public bool RetranslatedDueToKeyDifferences { get; set; }
    public string SkipReason { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public int InputTokens { get; set; }
    public int OutputTokens { get; set; }
}