using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// ModuleUtilities Component
	///  
	/// </summary>
    public partial class ModuleUtilities : BaseServerComponent, IModuleUtilities
    {
        public ModuleUtilities(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
        {
        }

        /// <summary>
        /// GetAvailableModules Method
        /// Get the list of availables modules, not used on any vehicle 
        /// </summary>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<DataObjectCollection<ModuleDataObject>>> GetAvailableModulesAsync(Guid dealerId, Dictionary<string, object> parameters = null)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                var queryStopwatch = Stopwatch.StartNew();

                var queryFilter = "(Status = @0 OR Status = null) AND Vehicle = null";
                var queryParametersList = new List<object> { (int)ModuleStatusEnum.Spare };
                int parameterIndex = 1;

                if (dealerId != Guid.Empty)
                {
                    queryFilter += $" AND DealerId = @{parameterIndex}";
                    queryParametersList.Add(dealerId);
                    parameterIndex++;
                }

                // Extract search parameters from the parameters dictionary
                string searchFilterPredicate = null;
                object[] searchFilterParameters = null;

                if (parameters != null)
                {
                    Debug.WriteLine($"[SEARCH] GetAvailableModulesAsync received parameters: {string.Join(", ", parameters.Keys)}");

                    // Check for filterPredicate parameter (used by client-side search)
                    if (parameters.TryGetValue("filterPredicate", out var filterPredicateValue) && filterPredicateValue != null)
                    {
                        searchFilterPredicate = filterPredicateValue.ToString();
                        Debug.WriteLine($"[SEARCH] Found filterPredicate: '{searchFilterPredicate}'");
                    }

                    // Check for filterParameters parameter (used by client-side search)
                    if (parameters.TryGetValue("filterParameters", out var filterParametersValue) && filterParametersValue != null)
                    {
                        var filterParametersJson = filterParametersValue.ToString();
                        Debug.WriteLine($"[SEARCH] Found filterParameters JSON: '{filterParametersJson}'");

                        if (!string.IsNullOrEmpty(filterParametersJson))
                        {
                            try
                            {
                                var filterParams = System.Text.Json.JsonSerializer.Deserialize<List<Dictionary<string, object>>>(filterParametersJson);
                                searchFilterParameters = filterParams?.Select(p => p.TryGetValue("Value", out var value) ? value : null).ToArray();
                                Debug.WriteLine($"[SEARCH] Parsed {searchFilterParameters?.Length ?? 0} search parameters: [{string.Join(", ", searchFilterParameters?.Select(p => $"'{p}'") ?? Array.Empty<string>())}]");
                            }
                            catch (Exception ex)
                            {
                                Debug.WriteLine($"[SEARCH] Failed to parse filterParameters JSON: {ex.Message}");
                                // If JSON parsing fails, ignore search parameters
                                searchFilterPredicate = null;
                                searchFilterParameters = null;
                            }
                        }
                    }
                }
                else
                {
                    Debug.WriteLine("[SEARCH] GetAvailableModulesAsync received no parameters dictionary");
                }

                // Combine base filter with search filter if provided
                if (!string.IsNullOrEmpty(searchFilterPredicate))
                {
                    // Adjust parameter indices in search predicate to account for existing parameters
                    var adjustedSearchPredicate = searchFilterPredicate;
                    if (searchFilterParameters != null)
                    {
                        for (int i = searchFilterParameters.Length - 1; i >= 0; i--)
                        {
                            adjustedSearchPredicate = adjustedSearchPredicate.Replace($"@{i}", $"@{parameterIndex + i}");
                        }
                    }

                    queryFilter = $"({queryFilter}) AND ({adjustedSearchPredicate})";
                    queryParametersList.AddRange(searchFilterParameters ?? Array.Empty<object>());
                    Debug.WriteLine($"[SEARCH] Final query with search: '{queryFilter}' with {queryParametersList.Count} parameters");
                }
                else
                {
                    Debug.WriteLine($"[SEARCH] Base query (no search): '{queryFilter}' with {queryParametersList.Count} parameters");
                }

                var result = await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, queryFilter, queryParametersList.ToArray());

                queryStopwatch.Stop();
                stopwatch.Stop();

                var resultCount = result?.Count ?? 0;
                if (stopwatch.ElapsedMilliseconds > 1000)
                {
                    Debug.WriteLine($"[PERF] GetAvailableModulesAsync took {stopwatch.ElapsedMilliseconds}ms (query: {queryStopwatch.ElapsedMilliseconds}ms), returned {resultCount} available modules (DealerId: {dealerId})");
                }

                return new ComponentResponse<DataObjectCollection<ModuleDataObject>>(new DataObjectCollection<ModuleDataObject>(result));
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Debug.WriteLine($"[PERF] GetAvailableModulesAsync failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}");
                throw;
            }
        }

        public async System.Threading.Tasks.Task<ComponentResponse<ModuleDataObject>> ResetCalibrationAsync(Guid moduleId, Dictionary<string, object> parameters = null)
        {
            ModuleDataObject module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { moduleId })).SingleOrDefault();

            if (module == null)
            {
                throw new GOServerException("Module not found.");
            }

            module.Calibration = 0;
            module.CalibrationResetDate = DateTime.UtcNow;
            module.BlueImpact = 0;
            module.AmberImpact = 0;
            module.RedImpact = 0;
            module.FSSSBase = 0;

            module = await _dataFacade.ModuleDataProvider.SaveAsync(module);

            return new ComponentResponse<ModuleDataObject>(module);
        }

        /// <summary>
        /// SwapModuleForVehicle Method
        /// Get the list of availables modules, not used on any vehicle 
        /// </summary>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> SwapModuleForVehicleAsync(System.Guid vehicleId, System.Guid newModuleId, System.String note, Dictionary<string, object> parameters = null)
        {
            var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { vehicleId })).SingleOrDefault();
            var customer = await vehicle.LoadCustomerAsync();
            var site = await vehicle.LoadSiteAsync();
            var department = await vehicle.LoadDepartmentAsync();
            var newModule = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { newModuleId })).SingleOrDefault();
            var oldModule = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { vehicle.ModuleId1 })).SingleOrDefault();
            if (vehicle == null || newModule == null)
            {
                throw new GOServerException("Vehicle or Module not found.");
            }

            if (oldModule != null)
            {
                // append the note to the old module
                oldModule.Note = note;
                // change the status of the old module to RA - not returned to CI
                oldModule.Status = (ModuleStatusEnum)3; // RA - not returned to CI
                oldModule.ModuleType = (ModuleTypeEnum)0; // Mk3
                oldModule.SwapDate = DateTime.UtcNow;
                oldModule.FromCustomer = customer.CompanyName;
                oldModule.FromSite = site.Name;
                oldModule.FromDepartment = department.Name;
                // check if old module belongs to a dealer
                if (oldModule.DealerId == null)
                {
                    // append the dealer from vehicle to the old module
                    oldModule.DealerId = customer.DealerId;
                }
                await _dataFacade.ModuleDataProvider.SaveAsync(oldModule);

                // create Module Hisotry record for the swapped module
                var moduleHistory = _serviceProvider.GetRequiredService<ModuleHistoryDataObject>();
                moduleHistory.ModuleId = oldModule.Id;
                moduleHistory.EditDateTime = DateTime.UtcNow;
                moduleHistory.Status = (ModuleStatusEnum)oldModule.Status; // RA - not returned to CI
                moduleHistory.ModuleType = (ModuleTypeEnum)oldModule.ModuleType;
                moduleHistory.FromDeviceID = newModule.IoTDevice;
                moduleHistory.OldIoTDeviceId = oldModule.IoTDevice;
                moduleHistory.VehicleId = vehicle.Id;
                moduleHistory.CCID = oldModule.CCID;
                moduleHistory.RANumber = oldModule.RANumber;
                moduleHistory.TechNumber = oldModule.TechNumber;
                moduleHistory.SimCardNumber = oldModule.SimCardNumber;
                moduleHistory.SimCardDate = oldModule.SimCardDate;
                moduleHistory.SwapDateTime = DateTime.UtcNow;

                await _dataFacade.ModuleHistoryDataProvider.SaveAsync(moduleHistory);
            }


            // change the module of the vehicle
            vehicle.ModuleId1 = newModule.Id;
            // clear vehicle note
            vehicle.ModuleSwapNote = null;
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            newModule.Status = (ModuleStatusEnum)2; // Assinged to vehicle
            newModule.SwapDate = DateTime.UtcNow;
            newModule.FromCustomer = customer.CompanyName;
            newModule.FromSite = site.Name;
            newModule.FromDepartment = department.Name;
            await _dataFacade.ModuleDataProvider.SaveAsync(newModule);

            // create Module Hisotry record for the new module
            var moduleHistoryNew = _serviceProvider.GetRequiredService<ModuleHistoryDataObject>();
            moduleHistoryNew.ModuleId = newModule.Id;
            moduleHistoryNew.EditDateTime = DateTime.UtcNow;
            moduleHistoryNew.Status = (ModuleStatusEnum)newModule.Status; // Assinged to vehicle
            moduleHistoryNew.ModuleType = (ModuleTypeEnum)newModule.ModuleType;
            moduleHistoryNew.FromDeviceID = oldModule.IoTDevice;
            moduleHistoryNew.NewIoTDeviceId = newModule.IoTDevice;
            moduleHistoryNew.VehicleId = vehicle.Id;

            moduleHistoryNew.CCID = newModule.CCID;
            moduleHistoryNew.RANumber = newModule.RANumber;
            moduleHistoryNew.TechNumber = newModule.TechNumber;
            moduleHistoryNew.SimCardNumber = newModule.SimCardNumber;
            moduleHistoryNew.SimCardDate = newModule.SimCardDate;
            moduleHistoryNew.SwapDateTime = DateTime.UtcNow;

            await _dataFacade.ModuleHistoryDataProvider.SaveAsync(moduleHistoryNew);

            return new ComponentResponse<System.Boolean>(true);
        }
    }
}
