﻿<!--
// This is Generated Code
// You should not modify this code as it may be overwritten
// Generated By Generative Objects 
--> 
<div class="uiContainer list-container" id="{VIEWNAME}-{DISPLAYMODE}" data-bind="'css': { 'busy': {DATABINDROOT}StatusData.IsBusy(), 'disabled': !{DATABINDROOT}StatusData.IsEnabled() || {DATABINDROOT}StatusData.IsBusy() }" data-test-id="cc069432-218a-4fd5-8d62-192d1dbc75c0">
  <div id="{VIEWNAME}Control-{DISPLAYMODE}">
    <div class="hideElt" data-bind="css: { hideElt : false }">
      <h2 data-bind="visible: {DATABINDROOT}StatusData.ShowTitle(), i18n: {DATABINDROOT}StatusData.Title()"></h2>
      <div class="no-data-message" data-bind="'visible' : {DATABINDROOT}StatusData.IsEmpty() &amp;&amp; !{DATABINDROOT}StatusData.IsBusy(), i18n: 'entities/PersonToModelVehicleNormalAccessView/lists/PersonToModelVehicleMasterAccessViewList:messages.noDataMessage'"></div>
      <div class="loading-content" data-bind="'visible' : {DATABINDROOT}StatusData.IsEmpty() &amp;&amp; {DATABINDROOT}StatusData.IsBusy()">
        <span class="loading-content-image"></span>
        <span data-bind="text: FleetXQ.Web.Messages.loadingMessage"></span>
      </div>
      <div class="uiContainer list-item-container">
        <div class="row" data-bind="'visible' : !{DATABINDROOT}StatusData.IsEmpty(), 'foreach' : { data: {DATABINDROOT}viewModelCollection, as: '{LISTNAME}KOData' }" id="{VIEWNAME}Data-{DISPLAYMODE}">
          <div class="col-4 uiContainer">
            <div>{#false,PersonToModelVehicleMasterAccessViewForm,Form,,PersonToModelVehicleNormalAccessView\PersonToModelVehicleMasterAccessViewFormPartialView.html#}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
 
