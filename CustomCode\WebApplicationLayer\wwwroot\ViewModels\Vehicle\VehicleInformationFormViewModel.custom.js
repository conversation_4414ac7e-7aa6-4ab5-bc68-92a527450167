﻿(function () {
    // This is the vehicle grid in Service Check Report
    FleetXQ.Web.ViewModels.VehicleInformationFormViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        this.initialize = function () {
            this.IsCanruleVisible = function () {
                return ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true;
            }

            this.IsIsCanbusVisible = function () {
                return ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true;
            }

            self.viewmodel.StatusData.IsCanruleReadOnly = function () {
                return !ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true;
            };

            self.viewmodel.StatusData.IsIsCanbusReadOnly = function () {
                return !ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true;
            };

            self.viewmodel.StatusData.IsSerialNoReadOnly = function () {
                // Allow editing if it's a new vehicle OR if user is Administrator OR if user has CanEditVehicle permission
                var isNew = self.viewmodel.VehicleObject().Data.IsNew();
                var isAdmin = ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true;
                var canEditVehicle = ApplicationController.viewModel.security.currentUserClaims().CanEditVehicle === 'True';

                return !isNew && !isAdmin && !canEditVehicle;
            };

            self.viewmodel.StatusData.IsHireNoReadOnly = function () {
                // Allow editing if it's a new vehicle OR if user is Administrator OR if user has CanEditVehicle permission
                var isNew = self.viewmodel.VehicleObject().Data.IsNew();
                var isAdmin = ApplicationController.viewModel.security.currentUserClaims().role?.includes('Administrator') == true;
                var canEditVehicle = ApplicationController.viewModel.security.currentUserClaims().CanEditVehicle === 'True';

                return !isNew && !isAdmin && !canEditVehicle;
            };

            self.viewmodel.StatusData.IsCustomerReadOnly = function () {
                return !self.viewmodel.VehicleObject().Data.IsNew();
            };
            self.viewmodel.StatusData.IsModuleReadOnly = function () {
                return !self.viewmodel.VehicleObject().Data.IsNew();
            };
            self.viewmodel.StatusData.IsModule1ReadOnly = function () {
                return !self.viewmodel.VehicleObject().Data.IsNew();
            };

            self.viewmodel.selectiveLoadDataForModel = function (clearSelection) {
                if (clearSelection) {
                    // First clear the current selection
                    self.viewmodel.Model_lookupItem({ label: "", value: null });
                    self.viewmodel.Model_Name(null);
                }

                self.viewmodel.countModelElements(function (data) {
                    // HACK: This is a hack to load all models regardless of the threshold
                    self.viewmodel.getModelCollectionData();
                    self.viewmodel.Model_lookupMethod = self.viewmodel.getModelCollectionOneLevel;
                    self.viewmodel.Model_lookupMinLength(0);
                });
            };

            // Override getModule1CollectionData to add dealerId from customer
            self.viewmodel.getModule1CollectionData = function (callback) {
                self.viewmodel.isGetModule1CollectionBusy(true);

                var configuration = {};
                configuration.contextId = self.viewmodel.Module1ContextId;
                configuration.filterPredicate = "";

                // Get dealerId from customer if available
                var customer = self.viewmodel.VehicleObject().getCustomer();
                if (customer && customer.Data.DealerId) {
                    configuration.dealerId = customer.Data.DealerId();
                }

                configuration.successHandler = callback || self.viewmodel.onGetModule1CollectionDataSuccess;

                configuration.errorHandler = self.viewmodel.onGetModule1CollectionDataError;

                self.viewmodel.controller.applicationController.getProxyForComponent("ModuleUtilities").GetAvailableModules(configuration);
            };

            // Override getFilteredModule1CollectionData to add dealerId from customer
            self.viewmodel.getFilteredModule1CollectionData = function (searchValue, callback) {
                self.viewmodel.isGetModule1CollectionBusy(true);

                var configuration = {};
                configuration.contextId = self.viewmodel.Module1ContextId;
                configuration.filterPredicate = 'IoTDevice.Contains(@0)';
                configuration.filterParameters = JSON.stringify([{
                    "TypeName": "System.String",
                    "IsNullable": false,
                    "Value": searchValue
                }]);
                configuration.parameters = JSON.stringify({
                    filterPredicate: configuration.filterPredicate,
                    filterParameters: configuration.filterParameters
                });
                configuration.pageSize = 50;
                configuration.pageNumber = 1;

                // Get dealerId from customer if available
                var customer = self.viewmodel.VehicleObject().getCustomer();
                if (customer && customer.Data.DealerId) {
                    configuration.dealerId = customer.Data.DealerId();
                }

                configuration.successHandler = function (data) {
                    self.viewmodel.isGetModule1CollectionBusy(false);
                    if (callback) callback(data);
                };

                configuration.errorHandler = function (error) {
                    self.viewmodel.isGetModule1CollectionBusy(false);
                    self.viewmodel.onGetModule1CollectionDataError(error);
                };

                self.viewmodel.controller.applicationController.getProxyForComponent("ModuleUtilities").GetAvailableModules(configuration);
            };

            // override onVehicleObjectChanged
            self.viewmodel.onVehicleObjectChanged = function () {
                self.viewmodel.rebindSubFormFields();

                self.viewmodel.rebindLookups();

                self.viewmodel.StatusData.IsUIDirty(self.viewmodel.controller.ObjectsDataSet.isContextIdDirty(self.viewmodel.contextId));

                if (self.viewmodel.VehicleObject().Data.IsNew()) {
                    // Get filter values from sessionStorage
                    var filterValues = sessionStorage.getItem('vehicleFilterValues');
                    if (filterValues) {
                        try {
                            var filterData = JSON.parse(filterValues);

                            // Start the chain of loading after a longer initial delay
                            setTimeout(function () {
                                if (filterData.customerId) {
                                    self.loadDefaultCustomer(filterData.customerId, function () {
                                        // Customer loaded successfully, now handle site
                                        if (filterData.siteId) {
                                            self.loadDefaultSite(filterData.siteId, function () {
                                                // Site loaded successfully, now handle department
                                                if (filterData.departmentId) {
                                                    self.loadDefaultDepartment(filterData.departmentId);
                                                }
                                                // Clear the stored values after we're done using them
                                                sessionStorage.removeItem('vehicleFilterValues');
                                            });
                                        } else {
                                            // Clear the stored values if we don't need to load site
                                            sessionStorage.removeItem('vehicleFilterValues');
                                        }
                                    });
                                } else {
                                    // Clear the stored values if we don't need to load customer
                                    sessionStorage.removeItem('vehicleFilterValues');
                                }
                            }, 500); // Increased initial delay
                        } catch (error) {
                            console.error('Error parsing filter values:', error);
                            // Clear the stored values if there was an error
                            sessionStorage.removeItem('vehicleFilterValues');
                        }
                    }
                }
            };
        }

        this.loadDefaultCustomer = function (customerId, callback) {
            if (customerId == null) {
                if (callback) callback();
                return;
            }

            var configuration = {};
            configuration.contextId = self.viewmodel.CustomerContextId;
            configuration.pks = {
                Id: customerId
            };

            configuration.successHandler = function (customer) {
                self.viewmodel.VehicleObject().Data.CustomerId(customerId);
                self.viewmodel.Customer_CompanyName(customer.Data.CompanyName());
                self.viewmodel.Customer_lookupItem({
                    label: customer.Data.CompanyName(),
                    value: customer,
                    selectable: true
                });

                // Wait a bit before triggering the site lookup refresh
                setTimeout(function () {
                    self.viewmodel.selectiveLoadDataForSite(true);
                    // Wait for the site data to be available
                    setTimeout(function () {
                        if (callback) callback();
                    }, 300);
                }, 300);
            };

            configuration.errorHandler = function (error) {
                console.error("Error loading default customer:", error);
                if (callback) callback();
            };

            self.viewmodel.DataStoreCustomer.LoadObject(configuration);
        };

        this.loadDefaultSite = function (siteId, callback) {
            if (siteId == null) {
                if (callback) callback();
                return;
            }

            var configuration = {};
            configuration.contextId = self.viewmodel.SiteContextId;
            configuration.pks = {
                Id: siteId
            };

            configuration.successHandler = function (site) {
                self.viewmodel.VehicleObject().Data.SiteId(siteId);
                self.viewmodel.Site_Name(site.Data.Name());
                self.viewmodel.Site_lookupItem({
                    label: site.Data.Name(),
                    value: site,
                    selectable: true
                });

                // Wait a bit before triggering the department lookup refresh
                setTimeout(function () {
                    self.viewmodel.selectiveLoadDataForDepartment(true);
                    // Wait for the department data to be available
                    setTimeout(function () {
                        if (callback) callback();
                    }, 300);
                }, 300);
            };

            configuration.errorHandler = function (error) {
                console.error("Error loading default site:", error);
                if (callback) callback();
            };

            self.viewmodel.DataStoreSite.LoadObject(configuration);
        };

        this.loadDefaultDepartment = function (departmentId) {
            if (departmentId == null) return;

            var configuration = {};
            configuration.contextId = self.viewmodel.DepartmentContextId;
            configuration.pks = {
                Id: departmentId
            };

            configuration.successHandler = function (department) {
                self.viewmodel.VehicleObject().Data.DepartmentId(departmentId);
                self.viewmodel.Department_Name(department.Data.Name());
                self.viewmodel.Department_lookupItem({
                    label: department.Data.Name(),
                    value: department,
                    selectable: true
                });
            };

            configuration.errorHandler = function (error) {
                console.error("Error loading default department:", error);
            };

            self.viewmodel.DataStoreDepartment.LoadObject(configuration);
        };
    }
}());
