using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ORMSupportClasses;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
    /// Interface for ModuleUtilitiesAPI Component
    /// Web API bridge for ModuleUtilities functionality
    /// </summary>
    public interface IModuleUtilitiesAPI
    {
        /// <summary>
        /// GetAvailableModules API Method
        /// Web API endpoint for getting available modules with search support
        /// </summary>
        /// <param name="dealerId">Dealer ID to filter modules</param>
        /// <param name="filterPredicate">Search filter predicate</param>
        /// <param name="filterParameters">Search filter parameters</param>
        /// <param name="parameters">Additional parameters dictionary</param>
        /// <returns>Collection of available modules</returns>
        Task<ComponentResponse<DataObjectCollection<ModuleDataObject>>> GetAvailableModulesAsync(
            Guid? dealerId = null, 
            string filterPredicate = null, 
            string filterParameters = null, 
            Dictionary<string, object> parameters = null);

        /// <summary>
        /// ResetCalibration API Method
        /// Web API endpoint for resetting module calibration
        /// </summary>
        /// <param name="moduleId">Module ID to reset calibration for</param>
        /// <param name="parameters">Additional parameters</param>
        /// <returns>Updated module data object</returns>
        Task<ComponentResponse<ModuleDataObject>> ResetCalibrationAsync(Guid moduleId, Dictionary<string, object> parameters = null);

        /// <summary>
        /// SwapModuleForVehicle API Method
        /// Web API endpoint for swapping modules between vehicles
        /// </summary>
        /// <param name="vehicleId">Vehicle ID</param>
        /// <param name="newModuleId">New module ID</param>
        /// <param name="note">Swap note</param>
        /// <param name="parameters">Additional parameters</param>
        /// <returns>Success status</returns>
        Task<ComponentResponse<System.Boolean>> SwapModuleForVehicleAsync(
            System.Guid vehicleId, 
            System.Guid newModuleId, 
            System.String note, 
            Dictionary<string, object> parameters = null);
    }
}