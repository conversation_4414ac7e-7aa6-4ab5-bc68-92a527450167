# FleetXQ Auto-Login Bypass Setup

This document explains how to configure and use the auto-login bypass functionality for FleetXQ development.

## Overview

The auto-login bypass feature automatically authenticates users with Admin/Admin credentials and redirects them directly to the Dashboard page (`/?originalVirtualDir=#!/Dashboard`) when running the application in development mode.

## Features

- **Automatic Authentication**: Bypasses the login form entirely using API authentication
- **Fallback Form Login**: If API authentication fails, automatically fills and submits the login form
- **Development Only**: Only enabled in development environments (localhost)
- **Dashboard Redirect**: Automatically navigates to the Dashboard after successful authentication
- **Configurable**: Easy to enable/disable and customize credentials

## Implementation Details

The auto-login functionality is implemented through two components:

### 1. External Script File
- **Location**: `CustomCode/WebApplicationLayer/wwwroot/Custom/AutoLoginBypass.js`
- **Features**: Comprehensive auto-login with retry logic, error handling, and debugging
- **Configuration**: Exposed via `window.FleetXQ_AutoLogin` object

### 2. Inline Fallback Script
- **Location**: Embedded in `CustomCode/ServiceLayer/Middleware/SubdomainMiddleware.cs`
- **Purpose**: Provides auto-login functionality even if external script is not available
- **Environment Detection**: Only runs on localhost or development environments

## Configuration

### External Script Configuration

Edit `CustomCode/WebApplicationLayer/wwwroot/Custom/AutoLoginBypass.js`:

```javascript
var AUTO_LOGIN_CONFIG = {
    enabled: true,              // Set to false to disable auto-login
    username: 'Admin',          // Default username
    password: 'Admin',          // Default password
    dashboardUrl: '/?originalVirtualDir=#!/Dashboard', // Target URL
    maxRetries: 3,              // Number of retry attempts
    retryDelay: 1000           // Delay between retries (ms)
};
```

### Inline Script Configuration

The inline script is automatically configured for development environments and uses:
- Username: `Admin`
- Password: `Admin`
- Dashboard URL: `/?originalVirtualDir=#!/Dashboard`

## Environment Detection

Auto-login is automatically enabled when:
- `window.location.hostname` is `localhost`
- `window.location.hostname` contains `localhost`
- `window.location.port` is `53052` (FleetXQ development port)

## How It Works

### Method 1: API Authentication (Primary)
1. Fetches CSRF token from `/dataset/api/goservices/csrf-token`
2. Authenticates via `/dataset/api/gosecurityprovider/authenticate`
3. Stores authentication tokens in session storage
4. Redirects to Dashboard

### Method 2: Form-based Authentication (Fallback)
1. Detects login page using test IDs
2. Fills username field and clicks first login button
3. Fills password field and clicks second login button
4. Waits for authentication completion and redirects

## Usage

### For Development

1. **Start the FleetXQ application** in development mode
2. **Navigate to the application** - you'll be automatically logged in
3. **Dashboard loads automatically** without manual login

### For Testing

Access debugging features via browser console:

```javascript
// Check if auto-login is enabled
window.FleetXQ_AutoLogin.config.enabled

// Force auto-login attempt
window.FleetXQ_AutoLogin.forceLogin()

// Check if currently on login page
window.FleetXQ_AutoLogin.isLoginPage()

// Check if user is authenticated
window.FleetXQ_AutoLogin.isAuthenticated()
```

## Disabling Auto-Login

### Temporary Disable
```javascript
// In browser console
window.FleetXQ_AutoLogin.config.enabled = false;
```

### Permanent Disable
Edit `AutoLoginBypass.js`:
```javascript
var AUTO_LOGIN_CONFIG = {
    enabled: false,  // Set to false
    // ... other config
};
```

## Security Considerations

- **Development Only**: Auto-login is designed to only work in development environments
- **Localhost Detection**: Multiple checks ensure it only runs on localhost
- **No Production Impact**: The inline script specifically checks for development environment
- **Easy to Disable**: Can be quickly disabled by changing configuration

## Troubleshooting

### Auto-login Not Working

1. **Check Console**: Look for `[AutoLogin]` messages in browser console
2. **Verify Environment**: Ensure you're running on localhost
3. **Check Configuration**: Verify `enabled: true` in configuration
4. **Network Issues**: Check if authentication API endpoints are accessible

### Common Issues

**Issue**: Script not loading
**Solution**: Verify file exists at `CustomCode/WebApplicationLayer/wwwroot/Custom/AutoLoginBypass.js`

**Issue**: Authentication fails
**Solution**: Verify Admin/Admin credentials are valid in your environment

**Issue**: Redirect not working
**Solution**: Check Dashboard URL is correct and accessible

### Debug Logging

Enable detailed logging by opening browser console and looking for:
- `[AutoLogin]` - External script messages
- `[AutoLogin-Inline]` - Inline script messages

## Advanced Configuration

### Custom Credentials

To use different credentials, modify the configuration:

```javascript
var AUTO_LOGIN_CONFIG = {
    username: 'your-username',
    password: 'your-password',
    // ... other settings
};
```

### Custom Dashboard URL

To redirect to a different page:

```javascript
var AUTO_LOGIN_CONFIG = {
    dashboardUrl: '/?originalVirtualDir=#!/YourPage',
    // ... other settings
};
```

### Environment-Specific Settings

You can add additional environment checks:

```javascript
// Only enable for specific subdomains
var isDevelopment = window.location.hostname === 'dev.localhost' || 
                   window.location.hostname === 'test.localhost';
```

## Files Modified

1. `CustomCode/WebApplicationLayer/wwwroot/Custom/AutoLoginBypass.js` - Main auto-login script
2. `CustomCode/ServiceLayer/Middleware/SubdomainMiddleware.cs` - Middleware injection
3. `CustomCode/WebApplicationLayer/AUTO_LOGIN_SETUP.md` - This documentation

## Testing the Implementation

1. **Start FleetXQ** in development mode
2. **Clear browser session** (to ensure clean state)
3. **Navigate to application** - should auto-login and redirect to Dashboard
4. **Check console logs** for auto-login messages
5. **Verify Dashboard loads** with admin user privileges

The auto-login bypass should work seamlessly in development while being completely disabled in production environments.
