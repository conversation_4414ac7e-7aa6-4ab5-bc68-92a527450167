# ModuleUtilities Search Functionality Fix

## Problem Analysis

The search functionality for the ModuleUtilities.GetAvailableModules endpoint was not working because:

1. **Missing API Bridge**: The frontend JavaScript was calling `getProxyForComponent("ModuleUtilities").GetAvailableModules()` but there was no proper Web API proxy component to handle the HTTP requests and parameter transmission.

2. **Parameter Transmission Gap**: While the frontend was correctly building search parameters (`filterPredicate`, `filterParameters`) and the backend `ModuleUtilities.GetAvailableModulesAsync` method was set up to handle them, there was no connection between the two layers.

3. **Routing Issue**: The component proxy was trying to call `/dataset/api/moduleutilities/` but this endpoint wasn't properly mapped to handle the search parameters.

## Solution Implemented

### 1. Created ModuleUtilitiesAPI Component

**File**: `./CustomCode/BusinessLayerServerComponents/ModuleUtilitiesAPI.cs`

This new API component serves as a bridge between the frontend and the existing ModuleUtilities component:

```csharp
public async Task<ComponentResponse<DataObjectCollection<ModuleDataObject>>> GetAvailableModulesAsync(
    Guid? dealerId = null, 
    string filterPredicate = null, 
    string filterParameters = null, 
    Dictionary<string, object> parameters = null)
{
    // Create parameters dictionary to pass search data
    var searchParameters = parameters ?? new Dictionary<string, object>();

    // Add search parameters if provided
    if (!string.IsNullOrEmpty(filterPredicate))
    {
        searchParameters["filterPredicate"] = filterPredicate;
    }

    if (!string.IsNullOrEmpty(filterParameters))
    {
        searchParameters["filterParameters"] = filterParameters;
    }

    // Call the underlying ModuleUtilities component
    return await _moduleUtilities.GetAvailableModulesAsync(dealerId ?? Guid.Empty, searchParameters);
}
```

### 2. Created ModuleUtilitiesAPI Interface

**File**: `./CustomCode/BusinessLayerServerComponents/IModuleUtilitiesAPI.cs`

Defines the contract for the API component with proper parameter signatures.

### 3. Created JavaScript Proxy Component

**File**: `./CustomCode/WebApplicationLayer/wwwroot/Model/Components/ModuleUtilitiesProxy.js`

This proxy handles the frontend-to-backend communication:

```javascript
this.GetAvailableModules = function (configuration) {
    // Prepare the data object
    var requestData = {
        dateformat: "ISO8601"
    };

    // Add dealerId if provided
    if (configuration.dealerId) {
        requestData.dealerId = configuration.dealerId;
    }

    // Add search parameters if provided
    if (configuration.filterPredicate) {
        requestData.filterPredicate = configuration.filterPredicate;
        console.log("[SEARCH DEBUG] Adding filterPredicate:", configuration.filterPredicate);
    }

    if (configuration.filterParameters) {
        requestData.filterParameters = configuration.filterParameters;
        console.log("[SEARCH DEBUG] Adding filterParameters:", configuration.filterParameters);
    }

    // Make AJAX call to proper endpoint
    self.GetAvailableModulesRequest = $.ajax({
        url: self.serviceUrl + "getavailablemodules",
        dataType: "json",
        type: "POST",
        headers: {
            'X-CSRF-TOKEN': FleetXQ.Web.Application.CSRF_TOKEN,
        },
        data: requestData,
        // ... success/error handlers
    });
};
```

### 4. Added Comprehensive Testing

**File**: `./CustomCode/BusinessLayerServerComponentsTests/FleetXQ.BusinessLayer.Components.Server.Custom.Tests/ModuleUtilitiesAPITest.cs`

Created unit tests to verify:
- Parameter passing from API to utilities component
- Search filtering functionality
- Error handling for invalid parameters

## How the Fix Works

### Frontend Flow
1. User types in search field
2. `getFilteredModuleCollectionData()` is called with search value
3. JavaScript builds configuration object with:
   - `filterPredicate`: `'IoTDevice.Contains(@0)'`
   - `filterParameters`: JSON array with search value
   - `parameters`: JSON string containing search parameters
4. `ModuleUtilitiesProxy.GetAvailableModules()` sends AJAX POST to `/dataset/api/moduleutilitiesapi/getavailablemodules`

### Backend Flow
1. Web API routes request to `ModuleUtilitiesAPI.GetAvailableModulesAsync()`
2. API component extracts search parameters from request
3. Parameters are passed to `ModuleUtilities.GetAvailableModulesAsync()`
4. Database query is built with search filters:
   ```sql
   ((Status = @0 OR Status = null) AND Vehicle = null) AND (IoTDevice.Contains(@1))
   ```
5. Filtered results are returned through the component chain

## Testing the Fix

### 1. Frontend Testing
Open browser developer tools and monitor the Network tab when performing searches:

```javascript
// Should see POST request to /dataset/api/moduleutilitiesapi/getavailablemodules
// With form data containing:
// - filterPredicate: "IoTDevice.Contains(@0)"
// - filterParameters: "[{\"TypeName\":\"System.String\",\"IsNullable\":false,\"Value\":\"SEARCH_TERM\"}]"
// - dealerId: (if applicable)
```

### 2. Backend Testing
Check the debug output in the application logs:

```
[API] ModuleUtilitiesAPI.GetAvailableModulesAsync called with dealerId: 00000000-0000-0000-0000-000000000000
[API] filterPredicate: 'IoTDevice.Contains(@0)'
[API] filterParameters: '[{"TypeName":"System.String","IsNullable":false,"Value":"TEST123"}]'
[SEARCH] GetAvailableModulesAsync received parameters: filterPredicate, filterParameters
[SEARCH] Found filterPredicate: 'IoTDevice.Contains(@0)'
[SEARCH] Final query with search: '((Status = @0 OR Status = null) AND Vehicle = null) AND (IoTDevice.Contains(@1))' with 2 parameters
```

### 3. Functional Testing
1. Navigate to Vehicle creation form
2. Click on Device ID dropdown
3. Type a search term (e.g., "ABC123")
4. Verify that only modules containing "ABC123" in their IoTDevice field are returned
5. Clear search and verify all available modules are shown again

## Expected Behavior After Fix

### Before Fix
- All searches returned the same results (all available modules)
- No search parameters were transmitted to backend
- Backend always used base query without search filters

### After Fix
- Search terms filter the module list in real-time
- Different search terms return different result sets
- Empty search returns all available modules
- Search parameters are properly logged and debuggable

## Files Modified/Created

### New Files
1. `./CustomCode/BusinessLayerServerComponents/ModuleUtilitiesAPI.cs`
2. `./CustomCode/BusinessLayerServerComponents/IModuleUtilitiesAPI.cs`
3. `./CustomCode/WebApplicationLayer/wwwroot/Model/Components/ModuleUtilitiesProxy.js`
4. `./CustomCode/BusinessLayerServerComponentsTests/.../ModuleUtilitiesAPITest.cs`

### Existing Files
The existing `ModuleUtilities.cs` already had the search functionality implemented - it just needed the proper API bridge to receive the parameters.

## Deployment Notes

1. The new ModuleUtilitiesAPI component will need to be registered in the dependency injection container (if not auto-registered)
2. The JavaScript proxy will be automatically loaded by the framework
3. No database changes are required
4. The fix is backward compatible - existing non-search calls will continue to work

## Debugging Tips

If search still doesn't work after deployment:

1. **Check browser console** for any JavaScript errors or failed AJAX requests
2. **Monitor network requests** to verify POST data is being sent correctly
3. **Check server logs** for the debug messages showing parameter reception
4. **Verify API routing** by testing the endpoint directly with a tool like Postman
5. **Check dependency injection** to ensure ModuleUtilitiesAPI is properly registered

## Performance Considerations

The search functionality uses database-level filtering, so it's efficient even with large module datasets. The indexed search on `IoTDevice` field ensures good performance for typical search patterns.