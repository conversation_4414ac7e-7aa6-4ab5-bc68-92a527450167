# ModuleUtilities Search Functionality - Working Examples

## How the Search Now Works

The `GetAvailableModulesAsync` method now properly processes search parameters passed from the client-side and combines them with the existing status and dealer filters.

## Query Building Process

### Step 1: Base Query
```sql
-- Always starts with this base filter
(Status = @0 OR Status = null) AND Vehicle = null
-- Parameters: [@0 = ModuleStatusEnum.Spare (1)]
```

### Step 2: Add Dealer Filter (if provided)
```sql
-- If dealerId is not empty, adds:
(Status = @0 OR Status = null) AND Vehicle = null AND DealerId = @1
-- Parameters: [@0 = 1, @1 = dealerGuid]
```

### Step 3: Add Search Filter (if provided)
```sql
-- If search parameters exist, combines with AND:
((Status = @0 OR Status = null) AND Vehicle = null AND DealerId = @1) AND (IoTDevice.Contains(@2))
-- Parameters: [@0 = 1, @1 = dealerGuid, @2 = "searchTerm"]
```

## Client-Side Examples

### Example 1: Search by IoT Device ID
```javascript
// Client sends this configuration:
var configuration = {
    filterPredicate: 'IoTDevice.Contains("device123")',
    dealerId: dealerGuid
};

// Results in SQL:
// ((Status = @0 OR Status = null) AND Vehicle = null AND DealerId = @1) AND (IoTDevice.Contains(@2))
// Parameters: [1, dealerGuid, "device123"]
```

### Example 2: Search by CCID
```javascript
// Client sends this configuration:
var configuration = {
    filterPredicate: 'CCID.Contains("CC001")',
    dealerId: dealerGuid
};

// Results in SQL:
// ((Status = @0 OR Status = null) AND Vehicle = null AND DealerId = @1) AND (CCID.Contains(@2))
// Parameters: [1, dealerGuid, "CC001"]
```

### Example 3: Complex Search with JSON Parameters
```javascript
// Client sends this configuration:
var configuration = {
    filterPredicate: 'IoTDevice.Contains(@0) && CCID.Contains(@1)',
    filterParameters: '[{"TypeName":"System.String","Value":"device"},{"TypeName":"System.String","Value":"CC"}]',
    dealerId: dealerGuid
};

// Results in SQL:
// ((Status = @0 OR Status = null) AND Vehicle = null AND DealerId = @1) AND (IoTDevice.Contains(@2) && CCID.Contains(@3))
// Parameters: [1, dealerGuid, "device", "CC"]
```

## Expected Results

### Before Fix (Broken Behavior)
- **Search "device123"** → Returns ALL available modules for dealer (ignores search)
- **Search "CC001"** → Returns ALL available modules for dealer (ignores search)
- **Search "xyz"** → Returns ALL available modules for dealer (ignores search)

### After Fix (Correct Behavior)
- **Search "device123"** → Returns only modules where IoTDevice contains "device123"
- **Search "CC001"** → Returns only modules where CCID contains "CC001"  
- **Search "xyz"** → Returns empty collection (no matches)
- **No search** → Returns all available modules (backward compatible)

## Error Handling Examples

### Invalid JSON Parameters
```javascript
// Client sends invalid JSON:
var configuration = {
    filterPredicate: 'IoTDevice.Contains(@0)',
    filterParameters: 'invalid json string',
    dealerId: dealerGuid
};

// System gracefully handles this:
// - Ignores the search parameters
// - Falls back to base query: (Status = @0 OR Status = null) AND Vehicle = null AND DealerId = @1
// - Returns all available modules for dealer
```

### Missing Parameters
```javascript
// Client sends only filterPredicate without filterParameters:
var configuration = {
    filterPredicate: 'IoTDevice.Contains("device123")',
    dealerId: dealerGuid
};

// System handles this correctly:
// - Uses the filterPredicate as-is (no parameter substitution needed)
// - Results in: ((Status = @0 OR Status = null) AND Vehicle = null AND DealerId = @1) AND (IoTDevice.Contains("device123"))
```

## Performance Considerations

1. **Single Database Query**: All filters are combined into one SQL query
2. **Existing Indexes**: Leverages existing indexes on Status, DealerId, and IoTDevice fields
3. **Performance Logging**: Maintains existing performance monitoring
4. **Minimal Overhead**: When no search parameters provided, performance is identical to before

## Backward Compatibility

✅ **Existing calls work unchanged**:
```javascript
// This still works exactly as before:
var configuration = {
    dealerId: dealerGuid
};
// Returns all available modules for dealer
```

✅ **No breaking changes** to method signature or return types

✅ **Graceful degradation** when search parameters are invalid or missing
