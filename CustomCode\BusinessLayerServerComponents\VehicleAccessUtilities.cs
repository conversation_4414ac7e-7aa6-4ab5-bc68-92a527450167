﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NHibernate.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace FleetXQ.BusinessLayer.Components.Server
{

    /// <summary>
	/// VehicleAccessUtilities Component
	///  
	/// </summary>
    public partial class VehicleAccessUtilities : BaseServerComponent, IVehicleAccessUtilities
    {
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly ILoggingService _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        private readonly IAuthentication _authentication;

        public VehicleAccessUtilities(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler, ILoggingService logger, IServiceScopeFactory serviceScopeFactory, IAuthentication authentication) : base(serviceProvider, configuration, dataFacade)
        {
            _deviceMessageHandler = deviceMessageHandler;
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
            _authentication = authentication;
        }

        /////////////////////////////////////////////////////////////////////////////////////
        // New methods for updating site, department, model and vehicle accesses in batch
        /////////////////////////////////////////////////////////////////////////////////////

        public async Task<ComponentResponse<bool>> CopyUserVehicleAccessAsync(Guid personId, Guid[] driverIds, Dictionary<string, object> parameters = null)
        {
            var person = await GetPersonAsync(personId);

            foreach (var driverId in driverIds)
            {
                var driver = _serviceProvider.GetRequiredService<DriverDataObject>();
                driver.Id = driverId;
                driver = await _dataFacade.DriverDataProvider.GetAsync(driver, includes: new List<string> {
                    "Card",
                    "Card.SiteVehicleNormalCardAccessItems",
                    "Card.DepartmentVehicleNormalCardAccessItems",
                    "Card.ModelVehicleNormalCardAccessItems",
                    "Card.PerVehicleNormalCardAccessItems" });

                await driver.LoadCardAsync(skipSecurity: true);

                if (driver.Card == null)
                {
                    continue;
                }

                // Delete current accesses
                foreach (var item in driver.Card.SiteVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }

                foreach (var item in driver.Card.DepartmentVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }

                foreach (var item in driver.Card.ModelVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }

                foreach (var item in driver.Card.PerVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }

                //Copy accesses
                foreach (var item in person.Driver.Card.SiteVehicleNormalCardAccessItems)
                {
                    var siteAccess = _serviceProvider.GetRequiredService<SiteVehicleNormalCardAccessDataObject>();
                    siteAccess.SiteId = item.SiteId;
                    siteAccess.PermissionId = item.PermissionId;

                    driver.Card.SiteVehicleNormalCardAccessItems.Add(siteAccess);
                }

                foreach (var item in person.Driver.Card.DepartmentVehicleNormalCardAccessItems)
                {
                    var deptAccess = _serviceProvider.GetRequiredService<DepartmentVehicleNormalCardAccessDataObject>();
                    deptAccess.DepartmentId = item.DepartmentId;
                    deptAccess.PermissionId = item.PermissionId;
                    driver.Card.DepartmentVehicleNormalCardAccessItems.Add(deptAccess);
                }

                foreach (var item in person.Driver.Card.ModelVehicleNormalCardAccessItems)
                {
                    var modelAccess = _serviceProvider.GetRequiredService<ModelVehicleNormalCardAccessDataObject>();
                    modelAccess.ModelId = item.ModelId;
                    modelAccess.DepartmentId = item.DepartmentId;
                    modelAccess.PermissionId = item.PermissionId;
                    driver.Card.ModelVehicleNormalCardAccessItems.Add(modelAccess);
                }

                foreach (var item in person.Driver.Card.PerVehicleNormalCardAccessItems)
                {
                    var vehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                    vehicleAccess.VehicleId = item.VehicleId;
                    vehicleAccess.PermissionId = item.PermissionId;

                    driver.Card.PerVehicleNormalCardAccessItems.Add(vehicleAccess);
                }

                await _dataFacade.DriverDataProvider.SaveAsync(driver, skipSecurity: true);
            }

            return new ComponentResponse<System.Boolean>(true);
        }

        public async Task<ComponentResponse<DataObjectCollection<PerVehicleNormalCardAccessDataObject>>> CreateOnDemandAccessesAsync(Guid[] cardIds, Guid vehicleId, Dictionary<string, object> parameters)
        {
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = vehicleId;
            vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);

            if (vehicle == null)
            {
                return new ComponentResponse<DataObjectCollection<PerVehicleNormalCardAccessDataObject>>(new DataObjectCollection<PerVehicleNormalCardAccessDataObject>());
            }

            var permissionOnDemandUser = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { (int)PermissionLevelEnum.OnDemandUser }, skipSecurity: true)).SingleOrDefault();

            var cardIdsToAdd = new List<Guid>();

            var accessList = new List<PerVehicleNormalCardAccessDataObject>();

            foreach (var cardId in cardIds)
            {
                var card = _serviceProvider.GetRequiredService<CardDataObject>();
                card.Id = cardId;
                card = await _dataFacade.CardDataProvider.GetAsync(card);

                if (card == null)
                {
                    continue;
                }

                await card.LoadPerVehicleNormalCardAccessItemsAsync();

                var existingPerVehicleAccess = card.PerVehicleNormalCardAccessItems.FirstOrDefault(a => a.VehicleId == vehicleId && a.PermissionId == permissionOnDemandUser.Id);
                if (existingPerVehicleAccess == null)
                {
                    existingPerVehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                    existingPerVehicleAccess.VehicleId = vehicleId;
                    existingPerVehicleAccess.PermissionId = permissionOnDemandUser.Id;
                    card.PerVehicleNormalCardAccessItems.Add(existingPerVehicleAccess);

                    await _dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);
                }

                accessList.Add(existingPerVehicleAccess);
            }

            // Start sync in background
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            _ = Task.Run(async () =>
            {
                var syncStart = DateTime.UtcNow;
                try
                {
                    using var scope = _serviceScopeFactory.CreateScope();
                    var scopedDataFacade = scope.ServiceProvider.GetRequiredService<IDataFacade>();
                    var scopedDeviceTwinHandler = scope.ServiceProvider.GetRequiredService<IDeviceTwinHandler>();

                    await SyncDriversInScopeAsync(new List<Guid> { vehicleId }, scopedDataFacade, scopedDeviceTwinHandler, scope.ServiceProvider, currentUserId);

                    var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                    _logger?.LogInformation($"[PERF] SyncDriversAsync (background - on-demand create) completed in {syncDuration}ms for vehicle {vehicleId}");
                }
                catch (Exception ex)
                {
                    var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                    _logger?.LogError(ex, $"[PERF] SyncDriversAsync (background - on-demand create) failed after {syncDuration}ms: {ex.Message}");
                }
            });

            return new ComponentResponse<DataObjectCollection<PerVehicleNormalCardAccessDataObject>>(new DataObjectCollection<PerVehicleNormalCardAccessDataObject>(accessList));
        }

        public async Task<ComponentResponse<bool>> DeleteOnDemandAccessAsync(Guid accessId, Dictionary<string, object> parameters = null)
        {
            var access = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
            access.Id = accessId;
            access = await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetAsync(access);

            if (access != null)
            {
                access.IsMarkedForDeletion = true;

                await _dataFacade.PerVehicleNormalCardAccessDataProvider.DeleteAsync(access);

                // Start sync in background
                var userClaims = await _authentication.GetCurrentUserClaimsAsync();
                var currentUserId = userClaims?.UserId;

                _ = Task.Run(async () =>
                {
                    var syncStart = DateTime.UtcNow;
                    try
                    {
                        using var scope = _serviceScopeFactory.CreateScope();
                        var scopedDataFacade = scope.ServiceProvider.GetRequiredService<IDataFacade>();
                        var scopedDeviceTwinHandler = scope.ServiceProvider.GetRequiredService<IDeviceTwinHandler>();

                        await SyncDriversInScopeAsync(new List<Guid> { access.VehicleId }, scopedDataFacade, scopedDeviceTwinHandler, scope.ServiceProvider, currentUserId);

                        var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                        _logger?.LogInformation($"[PERF] SyncDriversAsync (background - on-demand delete) completed in {syncDuration}ms for vehicle {access.VehicleId}");
                    }
                    catch (Exception ex)
                    {
                        var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                        _logger?.LogError(ex, $"[PERF] SyncDriversAsync (background - on-demand delete) failed after {syncDuration}ms: {ex.Message}");
                    }
                });

                return new ComponentResponse<bool>(true);
            }

            return new ComponentResponse<bool>(false);
        }

        public async Task<ComponentResponse<DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>>> GetAccessesForDepartmentsAsync(
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses,
            Guid personId,
            int permissionLevel,
            Dictionary<string, object> parameters = null)
        {
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { permissionLevel })).SingleOrDefault();

            if (permission == null)
            {
                _logger?.LogWarning($"[GetAccessesForDepartmentsAsync] No permission found for level {permissionLevel}. Returning empty collection.");
                var emptyDataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                var emptyCollection = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> { ObjectsDataSet = emptyDataset };
                return new ComponentResponse<DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>>(emptyCollection);
            }

            var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            var departmentAccessCollection = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
            foreach (var siteAccess in personToSiteAccesses)
            {
                var site = _serviceProvider.GetRequiredService<SiteDataObject>();
                site.Id = siteAccess.SiteId;
                site = await _dataFacade.SiteDataProvider.GetAsync(site, includes: new List<string> { "DepartmentItems" }, skipSecurity: true);
                if (site == null)
                {
                    continue;
                }

                var departments = site.DepartmentItems;

                var departmentVehicleAccesses = departments.Select(department =>
                {
                    var deptAccess = _serviceProvider.GetRequiredService<PersonToDepartmentVehicleNormalAccessViewDataObject>();

                    deptAccess.DepartmentId = department.Id;
                    deptAccess.DepartmentName = department.Name;
                    deptAccess.PermissionId = permission.Id;
                    deptAccess.PersonId = personId;
                    deptAccess.HasAccess = true;
                    deptAccess.IsNew = false; // Set to false to prevent subscription handlers from running

                    // Properly add the object to the dataset's internal structure
                    dataset.AddObject(deptAccess);

                    return deptAccess;
                }).ToList();

                departmentAccessCollection.AddRange(departmentVehicleAccesses);
            }

            return new ComponentResponse<DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>>(departmentAccessCollection);
        }

        public async Task<ComponentResponse<DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>>> GetAccessesForModelsAsync(
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses,
            Guid personId,
            int permissionLevel,
            Dictionary<string, object> parameters = null)
        {
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { permissionLevel })).SingleOrDefault();

            if (permission == null)
            {
                _logger?.LogWarning($"[GetAccessesForModelsAsync] No permission found for level {permissionLevel}. Returning empty collection.");
                var emptyDataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                var emptyCollection = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> { ObjectsDataSet = emptyDataset };
                return new ComponentResponse<DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>>(emptyCollection);
            }

            var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            var modelAccessCollection = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };

            foreach (var deptAccess in personToDepartmentAccesses)
            {
                var models = new List<ModelDataObject>();
                var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                department.Id = deptAccess.DepartmentId;
                department = await _dataFacade.DepartmentDataProvider.GetAsync(department, includes: new List<string> { "Vehicles" });

                if (department == null)
                {
                    continue;
                }

                var vehicles = department.Vehicles;

                foreach (var vehicle in vehicles)
                {
                    var model = await vehicle.LoadModelAsync();
                    if (model != null && !models.Exists(x => x.Id == model.Id))
                    {
                        models.Add(model);
                    }
                }

                var accessesToAdd = models
                    .Select(model =>
                    {
                        var access = _serviceProvider.GetRequiredService<PersonToModelVehicleNormalAccessViewDataObject>();
                        access.ModelId = model.Id;
                        access.ModelName = $"{department.Name} : {model.Name}";
                        access.PermissionId = permission.Id;
                        access.DepartmentId = department.Id;
                        access.PersonId = personId;
                        access.HasAccess = true;
                        access.IsNew = false; // Set to false to prevent subscription handlers from running

                        // Properly add the object to the dataset's internal structure
                        dataset.AddObject(access);

                        return access;
                    })
                    .ToList();

                modelAccessCollection.AddRange(accessesToAdd);
            }

            return new ComponentResponse<DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>>(modelAccessCollection);
        }

        public async Task<ComponentResponse<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>>> GetAccessesForVehiclesAsync(
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses,
            Guid personId,
            int permissionLevel,
            Dictionary<string, object> parameters = null)
        {
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { (int)permissionLevel })).SingleOrDefault();

            if (permission == null)
            {
                _logger?.LogWarning($"[GetAccessesForVehiclesAsync] No permission found for level {permissionLevel}. Returning empty collection.");
                var emptyDataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                var emptyCollection = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> { ObjectsDataSet = emptyDataset };
                return new ComponentResponse<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>>(emptyCollection);
            }

            var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            var vehicleAccessCollection = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };

            var vehicles = new List<VehicleDataObject>();

            foreach (var deptAccess in personToDepartmentAccesses)
            {
                var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                department.Id = deptAccess.DepartmentId;
                department = await _dataFacade.DepartmentDataProvider.GetAsync(department, includes: new List<string> { "Vehicles" });

                if (department == null)
                {
                    continue;
                }

                var modelIds = personToModelAccesses.Select(a => a.ModelId).ToList();

                foreach (var vehicle in department.Vehicles)
                {
                    if (!vehicles.Exists(x => x.Id == vehicle.Id) && modelIds.Contains(vehicle.ModelId))
                    {
                        vehicles.Add(vehicle);
                    }
                }
            }

            var perVehicleAccessList = vehicles
                .Select(vehicle =>
                {
                    var access = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewDataObject>();
                    access.VehicleId = vehicle.Id;
                    access.HireNo = vehicle.HireNo;
                    access.PermissionId = permission.Id;
                    access.PersonId = personId;
                    access.HasAccess = true;
                    access.IsNew = false; // Set to false to prevent subscription handlers from running

                    // Properly add the object to the dataset's internal structure
                    dataset.AddObject(access);

                    return access;
                })
                .ToList();

            vehicleAccessCollection.AddRange(perVehicleAccessList);

            return new ComponentResponse<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>>(vehicleAccessCollection);
        }

        public async Task<ComponentResponse<bool>> UpdateAccessesForPersonAsync(DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses, DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses, DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses, DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> personToVehicleAccesses, Guid personId, int PermissionLevel, bool cascadeAddPermission, Dictionary<string, object> parameters = null)
        {
            // Use PermissionLevel parameter directly (now passed from API controller)
            var permissionLevel = PermissionLevel;

            // Log detailed information about what we're processing
            var sitePermissionIds = personToSiteAccesses?.Select(x => x.PermissionId).Distinct().ToList() ?? new List<Guid>();
            var deptPermissionIds = personToDepartmentAccesses?.Select(x => x.PermissionId).Distinct().ToList() ?? new List<Guid>();
            var modelPermissionIds = personToModelAccesses?.Select(x => x.PermissionId).Distinct().ToList() ?? new List<Guid>();
            var vehiclePermissionIds = personToVehicleAccesses?.Select(x => x.PermissionId).Distinct().ToList() ?? new List<Guid>();
            var allPermissionIds = sitePermissionIds.Union(deptPermissionIds).Union(modelPermissionIds).Union(vehiclePermissionIds).Distinct().ToList();

            _logger?.LogInformation($"[SIMPLIFIED] UpdateAccessesForPersonAsync using permissionLevel: {permissionLevel} for queue message");
            _logger?.LogInformation($"[SIMPLIFIED] Processing permission IDs: {string.Join(", ", allPermissionIds)} - Expected: {(permissionLevel == 1 ? "BED65D1D-F318-4CBA-8750-A5A19300E042 (Master)" : "92CFAA84-0F55-43E0-8622-6BF2B2584AAC (Normal)")}");

            try
            {
                _logger?.LogInformation($"[PERF] Starting user access update queue operation for person {personId}");

                // Get the current user for tracking
                var userClaims = await _authentication.GetCurrentUserClaimsAsync();
                var currentUserId = userClaims?.UserId;

                // Get the person to extract customer ID
                var person = await GetPersonAsync(personId);

                // Create the user access update message
                var correlationId = Guid.NewGuid().ToString();
                var userAccessMessage = new UserAccessUpdateMessage
                {
                    PersonId = personId,
                    CustomerId = person.CustomerId,
                    PersonToSiteAccessesJson = SerializeAccessCollection(personToSiteAccesses),
                    PersonToDepartmentAccessesJson = SerializeAccessCollection(personToDepartmentAccesses),
                    PersonToModelAccessesJson = SerializeAccessCollection(personToModelAccesses),
                    PersonToVehicleAccessesJson = CompressAccessCollection(personToVehicleAccesses),
                    CreatedAt = DateTime.UtcNow,
                    InitiatedByUserId = currentUserId,
                    CorrelationId = correlationId,
                    Priority = "Normal",
                    PermissionLevel = permissionLevel
                };

                // Send message to queue
                var queueService = _serviceProvider.GetRequiredService<IUserAccessQueueService>();
                await queueService.SendUserAccessUpdateMessageAsync(userAccessMessage);

                _logger?.LogInformation($"[SIMPLIFIED] User access update message queued for person {personId} with permission level {permissionLevel} and correlation {correlationId}");

                return new ComponentResponse<bool>(true);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"[PERF] Failed to queue user access update for person {personId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Internal method that performs the actual user access update processing
        /// This method is called by the Azure Function to process queued messages
        /// </summary>
        public async Task<ComponentResponse<bool>> UpdateAccessesForPersonInternalAsync(
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses,
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses,
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> personToVehicleAccesses,
            Guid personId,
            Dictionary<string, object> parameters = null)
        {
            // Extract permission level from parameters (passed from queue message)
            var permissionLevel = 3; // Default to normal driver
            if (parameters != null && parameters.ContainsKey("permissionLevel"))
            {
                permissionLevel = Convert.ToInt32(parameters["permissionLevel"]);
            }

            _logger?.LogInformation($"[SIMPLIFIED] UpdateAccessesForPersonInternalAsync using permissionLevel: {permissionLevel} for person {personId}");

            // Log all incoming permission IDs to understand the context
            var allIncomingPermissionIds = new List<Guid>();
            if (personToSiteAccesses?.Any() == true)
                allIncomingPermissionIds.AddRange(personToSiteAccesses.Select(a => a.PermissionId));
            if (personToDepartmentAccesses?.Any() == true)
                allIncomingPermissionIds.AddRange(personToDepartmentAccesses.Select(a => a.PermissionId));
            if (personToModelAccesses?.Any() == true)
                allIncomingPermissionIds.AddRange(personToModelAccesses.Select(a => a.PermissionId));
            if (personToVehicleAccesses?.Any() == true)
                allIncomingPermissionIds.AddRange(personToVehicleAccesses.Select(a => a.PermissionId));

            var incomingPermissionLevels = allIncomingPermissionIds
                .Distinct()
                .Select(pid => GetPermissionLevelFromId(pid))
                .Where(level => level.HasValue)
                .Select(level => level.Value)
                .Distinct()
                .OrderBy(level => level)
                .ToList();

            _logger?.LogInformation($"[PERF] Incoming permission levels detected: [{string.Join(", ", incomingPermissionLevels)}]");

            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Track permission level context globally across all access types for better fallback logic
            // Initialize with incoming permission levels to provide strong context hint
            var globalPermissionContext = new HashSet<int>(incomingPermissionLevels);

            // Sites
            Dictionary<(Guid SiteId, Guid PermissionId), SiteVehicleNormalCardAccessDataObject> existingSiteAccesses;
            try
            {
                existingSiteAccesses = card.SiteVehicleNormalCardAccessItems
                    .ToDictionary(
                        access => (access.SiteId, access.PermissionId),
                        access => access
                    );
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate site access records detected for PersonId: {personId}. " +
                    $"Using GroupBy to handle duplicates and taking the first occurrence. Key details: {argEx.Message}");

                // Handle duplicates by grouping and taking the first occurrence of each key
                existingSiteAccesses = card.SiteVehicleNormalCardAccessItems
                    .GroupBy(access => (access.SiteId, access.PermissionId))
                    .ToDictionary(
                        group => group.Key,
                        group => group.First()
                    );

                _logger?.LogInformation($"[PERF] Successfully resolved duplicate site access records. " +
                    $"Created dictionary with {existingSiteAccesses.Count} unique entries for PersonId: {personId}");
            }

            var (sitesToAdd, sitesToRemove) = await CategorizeAccessUpdates(
                personToSiteAccesses,
                existingSiteAccesses,
                person);

            // Capture permission levels from successful site access resolution
            if (personToSiteAccesses?.Any() == true)
            {
                var sitePermissionLevels = personToSiteAccesses
                    .Select(a => a.PermissionId)
                    .Distinct()
                    .Select(pid => GetPermissionLevelFromId(pid))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value);

                foreach (var level in sitePermissionLevels)
                {
                    globalPermissionContext.Add(level);
                }
            }

            foreach (var siteAccess in sitesToAdd)
            {
                var newAccess = CreateSiteAccess(siteAccess.SiteId, siteAccess.PermissionId);
                card.SiteVehicleNormalCardAccessItems.Add(newAccess);
            }

            foreach (var siteAccess in sitesToRemove)
            {
                existingSiteAccesses[(siteAccess.SiteId, siteAccess.PermissionId)].IsMarkedForDeletion = true;
            }

            // Departments
            Dictionary<(Guid DepartmentId, Guid PermissionId), DepartmentVehicleNormalCardAccessDataObject> existingDepartmentAccesses;
            try
            {
                existingDepartmentAccesses = card.DepartmentVehicleNormalCardAccessItems
                    .ToDictionary(
                        access => (access.DepartmentId, access.PermissionId),
                        access => access
                    );
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate department access records detected for PersonId: {personId}. " +
                    $"Using GroupBy to handle duplicates and taking the first occurrence. Key details: {argEx.Message}");

                // Handle duplicates by grouping and taking the first occurrence of each key
                existingDepartmentAccesses = card.DepartmentVehicleNormalCardAccessItems
                    .GroupBy(access => (access.DepartmentId, access.PermissionId))
                    .ToDictionary(
                        group => group.Key,
                        group => group.First()
                    );

                _logger?.LogInformation($"[PERF] Successfully resolved duplicate department access records. " +
                    $"Created dictionary with {existingDepartmentAccesses.Count} unique entries for PersonId: {personId}");
            }

            var (departmentsToAdd, departmentsToRemove) = await CategorizeAccessUpdates(
                personToDepartmentAccesses,
                existingDepartmentAccesses,
                person,
                globalPermissionContext);

            // Capture permission levels from successful department access resolution
            if (personToDepartmentAccesses?.Any() == true)
            {
                var deptPermissionLevels = personToDepartmentAccesses
                    .Select(a => a.PermissionId)
                    .Distinct()
                    .Select(pid => GetPermissionLevelFromId(pid))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value);

                foreach (var level in deptPermissionLevels)
                {
                    globalPermissionContext.Add(level);
                }
            }

            foreach (var deptAccess in departmentsToAdd)
            {
                var newAccess = CreateDepartmentAccess(deptAccess.DepartmentId, deptAccess.PermissionId);
                card.DepartmentVehicleNormalCardAccessItems.Add(newAccess);
            }

            foreach (var deptAccess in departmentsToRemove)
            {
                existingDepartmentAccesses[(deptAccess.DepartmentId, deptAccess.PermissionId)]
                    .IsMarkedForDeletion = true;
            }

            // Models  
            Dictionary<(Guid ModelId, Guid DepartmentId, Guid PermissionId), ModelVehicleNormalCardAccessDataObject> existingModelAccesses;
            try
            {
                existingModelAccesses = card.ModelVehicleNormalCardAccessItems
                    .ToDictionary(
                        access => (access.ModelId, access.DepartmentId, access.PermissionId),
                        access => access
                    );
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate model access records detected for PersonId: {personId}. " +
                    $"Using GroupBy to handle duplicates and taking the first occurrence. Key details: {argEx.Message}");

                // Handle duplicates by grouping and taking the first occurrence of each key
                existingModelAccesses = card.ModelVehicleNormalCardAccessItems
                    .GroupBy(access => (access.ModelId, access.DepartmentId, access.PermissionId))
                    .ToDictionary(
                        group => group.Key,
                        group => group.First()
                    );

                _logger?.LogInformation($"[PERF] Successfully resolved duplicate model access records. " +
                    $"Created dictionary with {existingModelAccesses.Count} unique entries for PersonId: {personId}");
            }

            var (modelsToAdd, modelsToRemove) = await CategorizeAccessUpdates(
                personToModelAccesses,
                existingModelAccesses,
                person,
                globalPermissionContext);

            foreach (var modelAccess in modelsToAdd)
            {
                var newAccess = CreateModelAccess(
                    modelAccess.ModelId,
                    modelAccess.DepartmentId,
                    modelAccess.PermissionId);

                card.ModelVehicleNormalCardAccessItems.Add(newAccess);
            }

            foreach (var modelAccess in modelsToRemove)
            {
                var key = (modelAccess.ModelId, modelAccess.DepartmentId, modelAccess.PermissionId);
                existingModelAccesses[key].IsMarkedForDeletion = true;
            }

            // Vehicles
            Dictionary<(Guid VehicleId, Guid PermissionId), PerVehicleNormalCardAccessDataObject> existingVehicleAccesses;
            try
            {
                existingVehicleAccesses = card.PerVehicleNormalCardAccessItems
                    .ToDictionary(
                        access => (access.VehicleId, access.PermissionId),
                        access => access
                    );
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate vehicle access records detected for PersonId: {personId}. " +
                    $"Using GroupBy to handle duplicates and taking the first occurrence. Key details: {argEx.Message}");

                // Handle duplicates by grouping and taking the first occurrence of each key
                existingVehicleAccesses = card.PerVehicleNormalCardAccessItems
                    .GroupBy(access => (access.VehicleId, access.PermissionId))
                    .ToDictionary(
                        group => group.Key,
                        group => group.First()
                    );

                _logger?.LogInformation($"[PERF] Successfully resolved duplicate vehicle access records. " +
                    $"Created dictionary with {existingVehicleAccesses.Count} unique entries for PersonId: {personId}");
            }

            var (vehiclesToAdd, vehiclesToRemove) = await CategorizeAccessUpdates(
                personToVehicleAccesses,
                existingVehicleAccesses,
                person,
                globalPermissionContext);

            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            foreach (var vehicleAccess in vehiclesToAdd)
            {
                var newAccess = CreateVehicleAccess(
                    vehicleAccess.VehicleId,
                    vehicleAccess.PermissionId);

                card.PerVehicleNormalCardAccessItems.Add(newAccess);
                updatedPerVehicleAccesses.Add(newAccess);
            }

            foreach (var vehicleAccess in vehiclesToRemove)
            {
                var key = (vehicleAccess.VehicleId, vehicleAccess.PermissionId);
                var existingAccess = existingVehicleAccesses[key];
                existingAccess.IsMarkedForDeletion = true;
                updatedPerVehicleAccesses.Add(existingAccess);
            }

            // Save changes and sync drivers
            try
            {
                await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);
                _logger?.LogInformation($"[PERF] Successfully saved person access changes for PersonId: {personId}");
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate key detected during person access save for PersonId: {personId}. " +
                    $"This indicates data integrity issues with duplicate access records. Skipping access save but continuing with sync operations. " +
                    $"Key details: {argEx.Message}");

                // Continue with sync operations even if save failed due to duplicate keys
            }
            catch (Exception saveEx)
            {
                _logger?.LogError(saveEx, $"[PERF] Unexpected error during person access save for PersonId: {personId}: {saveEx.Message}");
                throw; // Re-throw other exceptions as they indicate more serious issues
            }

            // Queue sync operations instead of running them directly
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Use the specific permission level passed from queue message
                var permissionLevels = new List<int> { permissionLevel };

                // Send individual message for each vehicle
                if (Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Production")
                {
                    for (int i = 0; i < vehicleIds.Count; i++)
                    {
                        var syncMessage = new VehicleSyncMessage
                        {
                            VehicleId = vehicleIds[i],
                            PersonId = personId,
                            CustomerId = person.CustomerId,
                            InitiatedByUserId = currentUserId,
                            CreatedAt = DateTime.UtcNow,
                            CorrelationId = correlationId,
                            Priority = "Normal",
                            SyncReason = $"UserAccessUpdate_Level{permissionLevel}",
                            VehicleSequence = i + 1,
                            TotalVehicles = vehicleIds.Count,
                            PermissionLevels = permissionLevels.Any() ? permissionLevels : null
                        };
                        await vehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                    }
                    _logger?.LogInformation($"[SIMPLIFIED] {vehicleIds.Count} vehicle sync messages queued for person {personId} (user access update - Level {permissionLevel}) with correlation {correlationId}");
                }
                else
                {
                    _logger?.LogWarning($"[{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}] skipping sync event for person {personId} (user access update - Level {permissionLevel})");
                }

                _logger?.LogInformation($"[SIMPLIFIED] {vehicleIds.Count} vehicle sync messages queued for person {personId} (user access update - Level {permissionLevel}) with correlation {correlationId}");
            }

            _logger?.LogInformation($"[PERF] Internal user access update processing completed for person {personId}");
            return new ComponentResponse<bool>(true);
        }

        /// <summary>
        /// Serializes access collection to JSON string
        /// </summary>
        /// <typeparam name="T">The type of access data object</typeparam>
        /// <param name="collection">The collection to serialize</param>
        /// <returns>JSON string representation of the collection</returns>
        private string SerializeAccessCollection<T>(DataObjectCollection<T> collection) where T : class, IDataObject
        {
            if (collection == null || collection.Count == 0)
            {
                return string.Empty;
            }

            try
            {
                var items = collection.Cast<T>().ToList();
                // Use Newtonsoft.Json which handles complex object graphs better
                return JsonConvert.SerializeObject(items, new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    TypeNameHandling = TypeNameHandling.Auto,
                    NullValueHandling = NullValueHandling.Ignore
                });
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"[PERF] Failed to serialize access collection of type {typeof(T).Name}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Compresses access collection to Base64-encoded GZip string to reduce message size
        /// </summary>
        /// <typeparam name="T">The type of access data object</typeparam>
        /// <param name="collection">The collection to compress</param>
        /// <returns>Base64-encoded compressed JSON string</returns>
        private string CompressAccessCollection<T>(DataObjectCollection<T> collection) where T : class, IDataObject
        {
            if (collection == null || collection.Count == 0)
            {
                return string.Empty;
            }

            try
            {
                var stopwatch = System.Diagnostics.Stopwatch.StartNew();
                var items = collection.Cast<T>().ToList();

                // Serialize to JSON first
                var json = JsonConvert.SerializeObject(items, new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    TypeNameHandling = TypeNameHandling.Auto,
                    NullValueHandling = NullValueHandling.Ignore
                });

                var originalSize = Encoding.UTF8.GetByteCount(json);

                // Compress using GZip
                using (var output = new MemoryStream())
                {
                    using (var gzip = new GZipStream(output, CompressionLevel.Optimal))
                    using (var writer = new StreamWriter(gzip, Encoding.UTF8))
                    {
                        writer.Write(json);
                    }

                    var compressedBytes = output.ToArray();
                    var compressedBase64 = Convert.ToBase64String(compressedBytes);
                    var compressedSize = compressedBytes.Length;

                    stopwatch.Stop();
                    var compressionRatio = (double)originalSize / compressedSize;
                    _logger?.LogInformation($"[PERF] Compressed {typeof(T).Name} collection: {originalSize} bytes -> {compressedSize} bytes (ratio: {compressionRatio:F2}x) in {stopwatch.ElapsedMilliseconds}ms");

                    return compressedBase64;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"[PERF] Failed to compress access collection of type {typeof(T).Name}, falling back to regular serialization");
                // Fallback to regular serialization if compression fails
                return SerializeAccessCollection(collection);
            }
        }

        public async Task<ComponentResponse<bool>> UpdateVehicleDepartmentAccessesForPersonAsync(
            Guid personId,
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updatedPersonToDepartmentAccesses,
            Dictionary<string, object> parameters = null)
        {
            // Extract permission level from parameters (passed from JavaScript)
            var permissionLevel = 3; // Default to normal driver
            if (parameters != null && parameters.ContainsKey("permissionLevel"))
            {
                permissionLevel = Convert.ToInt32(parameters["permissionLevel"]);
            }

            _logger?.LogInformation($"[SIMPLIFIED] UpdateVehicleDepartmentAccessesForPersonAsync using permissionLevel: {permissionLevel}");

            // Get person data
            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Create lookup for existing department accesses - filter by permission level
            var existingDepartmentAccesses = card.DepartmentVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .ToDictionary(
                    access => (access.DepartmentId, access.PermissionId),
                    access => access
                );

            // Categorize department access updates - simplified without guessing
            var (departmentsToAdd, departmentsToRemove) = await CategorizeAccessUpdatesSimplified(
                updatedPersonToDepartmentAccesses,
                existingDepartmentAccesses,
                person,
                permissionLevel);

            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process additions
            foreach (var deptAccess in departmentsToAdd)
            {
                // Create and add department access
                var newAccess = CreateDepartmentAccess(deptAccess.DepartmentId, deptAccess.PermissionId);
                card.DepartmentVehicleNormalCardAccessItems.Add(newAccess);

                // Load department data
                var department = await GetDepartmentAsync(deptAccess.DepartmentId);

                // Process model and vehicle access
                await AddAccessForModelsOfDepartmentAsync(
                    card,
                    department,
                    deptAccess.PermissionId);

                var vehicleAccesses = await AddAccessForVehiclesOfDepartmentAsync(
                    card,
                    department,
                    deptAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Process removals
            foreach (var deptAccess in departmentsToRemove)
            {
                // Mark existing access for deletion
                existingDepartmentAccesses[(deptAccess.DepartmentId, deptAccess.PermissionId)]
                    .IsMarkedForDeletion = true;

                // Load department data
                var department = await GetDepartmentAsync(deptAccess.DepartmentId);

                // Remove model and vehicle access
                await RemoveAccessForModelsOfDepartmentAsync(
                    card,
                    department,
                    deptAccess.PermissionId);

                var vehicleAccesses = await RemoveAccessForVehiclesOfDepartmentAsync(
                    card,
                    deptAccess.DepartmentId,
                    deptAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Save changes and sync drivers
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Queue sync operations instead of running them directly
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Use the specific permission level passed from JavaScript
                var permissionLevels = new List<int> { permissionLevel };

                // Send individual message for each vehicle
                for (int i = 0; i < vehicleIds.Count; i++)
                {
                    var syncMessage = new VehicleSyncMessage
                    {
                        VehicleId = vehicleIds[i],
                        PersonId = personId,
                        CustomerId = person.CustomerId,
                        InitiatedByUserId = currentUserId,
                        CreatedAt = DateTime.UtcNow,
                        CorrelationId = correlationId,
                        Priority = "Normal",
                        SyncReason = $"DepartmentAccessUpdate_Level{permissionLevel}",
                        VehicleSequence = i + 1,
                        TotalVehicles = vehicleIds.Count,
                        PermissionLevels = permissionLevels.Any() ? permissionLevels : null
                    };

                    await vehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                }

                _logger?.LogInformation($"[SIMPLIFIED] {vehicleIds.Count} vehicle sync messages queued for person {personId} (department access update - Level {permissionLevel}) with correlation {correlationId}");
            }

            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<bool>> UpdateVehicleModelAccessesForPersonAsync(
            Guid personId,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updateModelAccesses,
            Dictionary<string, object> parameters = null)
        {
            // Extract permission level from parameters (passed from JavaScript)
            var permissionLevel = 3; // Default to normal driver
            if (parameters != null && parameters.ContainsKey("permissionLevel"))
            {
                permissionLevel = Convert.ToInt32(parameters["permissionLevel"]);
            }

            _logger?.LogInformation($"[SIMPLIFIED] UpdateVehicleModelAccessesForPersonAsync using permissionLevel: {permissionLevel}");

            // Get person data
            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Create lookup for existing model accesses - filter by permission level
            var existingModelAccesses = card.ModelVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .ToDictionary(
                    access => (access.ModelId, access.DepartmentId, access.PermissionId),
                    access => access
                );

            // Categorize model access updates - simplified without guessing
            var (modelsToAdd, modelsToRemove) = await CategorizeModelAccessUpdatesSimplified(
                updateModelAccesses,
                existingModelAccesses,
                person,
                permissionLevel);

            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process additions
            foreach (var modelAccess in modelsToAdd)
            {
                // Create and add model access
                var newAccess = CreateModelAccess(
                    modelAccess.ModelId,
                    modelAccess.DepartmentId,
                    modelAccess.PermissionId);

                card.ModelVehicleNormalCardAccessItems.Add(newAccess);

                // Add vehicle access
                var vehicleAccesses = await AddAccessForVehiclesOfModelAsync(
                    card,
                    modelAccess.ModelId,
                    modelAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Process removals
            foreach (var modelAccess in modelsToRemove)
            {
                // Mark existing access for deletion
                var key = (modelAccess.ModelId, modelAccess.DepartmentId, modelAccess.PermissionId);
                existingModelAccesses[key].IsMarkedForDeletion = true;

                // Remove vehicle access
                var vehicleAccesses = await RemoveAccessForVehiclesOfModelAsync(
                    card,
                    modelAccess.ModelId,
                    modelAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Save changes and sync drivers
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Queue sync operations instead of running them directly
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Use the specific permission level passed from JavaScript
                var permissionLevels = new List<int> { permissionLevel };

                // Send individual message for each vehicle
                for (int i = 0; i < vehicleIds.Count; i++)
                {
                    var syncMessage = new VehicleSyncMessage
                    {
                        VehicleId = vehicleIds[i],
                        PersonId = personId,
                        CustomerId = person.CustomerId,
                        InitiatedByUserId = currentUserId,
                        CreatedAt = DateTime.UtcNow,
                        CorrelationId = correlationId,
                        Priority = "Normal",
                        SyncReason = $"ModelAccessUpdate_Level{permissionLevel}",
                        VehicleSequence = i + 1,
                        TotalVehicles = vehicleIds.Count,
                        PermissionLevels = permissionLevels.Any() ? permissionLevels : null
                    };

                    await vehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                }

                _logger?.LogInformation($"[SIMPLIFIED] {vehicleIds.Count} vehicle sync messages queued for person {personId} (model access update - Level {permissionLevel}) with correlation {correlationId}");
            }

            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<bool>> UpdateVehiclePerVehicleAccessesForPersonAsync(
            Guid personId,
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updatePerVehicleAccesses,
            Dictionary<string, object> parameters = null)
        {
            // Extract permission level from parameters (passed from JavaScript)
            var permissionLevel = 3; // Default to normal driver
            if (parameters != null && parameters.ContainsKey("permissionLevel"))
            {
                permissionLevel = Convert.ToInt32(parameters["permissionLevel"]);
            }

            _logger?.LogInformation($"[SIMPLIFIED] UpdateVehiclePerVehicleAccessesForPersonAsync using permissionLevel: {permissionLevel}");

            // Get person data
            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Create lookup for existing vehicle accesses - filter by permission level
            var existingVehicleAccesses = card.PerVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .ToDictionary(
                    access => (access.VehicleId, access.PermissionId),
                    access => access
                );

            // Categorize vehicle access updates - simplified without guessing
            var (vehiclesToAdd, vehiclesToRemove) = await CategorizeVehicleAccessUpdatesSimplified(
                updatePerVehicleAccesses,
                existingVehicleAccesses,
                person,
                permissionLevel);

            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process additions
            foreach (var vehicleAccess in vehiclesToAdd)
            {
                var newAccess = CreateVehicleAccess(
                    vehicleAccess.VehicleId,
                    vehicleAccess.PermissionId);

                card.PerVehicleNormalCardAccessItems.Add(newAccess);
                updatedPerVehicleAccesses.Add(newAccess);
            }

            // Process removals
            foreach (var vehicleAccess in vehiclesToRemove)
            {
                var key = (vehicleAccess.VehicleId, vehicleAccess.PermissionId);
                var existingAccess = existingVehicleAccesses[key];
                existingAccess.IsMarkedForDeletion = true;
                updatedPerVehicleAccesses.Add(existingAccess);
            }

            // Save changes and sync drivers
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Queue sync operations instead of running them directly
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Use the specific permission level passed from JavaScript
                var permissionLevels = new List<int> { permissionLevel };

                // Send individual message for each vehicle
                for (int i = 0; i < vehicleIds.Count; i++)
                {
                    var syncMessage = new VehicleSyncMessage
                    {
                        VehicleId = vehicleIds[i],
                        PersonId = personId,
                        CustomerId = person.CustomerId,
                        InitiatedByUserId = currentUserId,
                        CreatedAt = DateTime.UtcNow,
                        CorrelationId = correlationId,
                        Priority = "Normal",
                        SyncReason = $"VehicleAccessUpdate_Level{permissionLevel}",
                        VehicleSequence = i + 1,
                        TotalVehicles = vehicleIds.Count,
                        PermissionLevels = permissionLevels.Any() ? permissionLevels : null
                    };

                    await vehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                }

                _logger?.LogInformation($"[SIMPLIFIED] {vehicleIds.Count} vehicle sync messages queued for person {personId} (vehicle access update - Level {permissionLevel}) with correlation {correlationId}");
            }

            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<bool>> UpdateVehicleSiteAccessesForPersonAsync(
                                                    Guid personId,
    DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updatedPersonToSiteAccesses,
Dictionary<string, object> parameters = null)
        {
            // Extract permission level from parameters (passed from JavaScript)
            var permissionLevel = 3; // Default to normal driver
            if (parameters != null && parameters.ContainsKey("permissionLevel"))
            {
                permissionLevel = Convert.ToInt32(parameters["permissionLevel"]);
            }

            _logger?.LogInformation($"[SIMPLIFIED] UpdateVehicleSiteAccessesForPersonAsync using permissionLevel: {permissionLevel}");

            // Get person data
            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Create lookup for existing site accesses - filter by permission level
            var existingSiteAccesses = card.SiteVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .ToDictionary(
                    access => (access.SiteId, access.PermissionId),
                    access => access
                );

            // Categorize site access updates - simplified without guessing
            var (sitesToAdd, sitesToRemove) = await CategorizeSiteAccessUpdatesSimplified(
                updatedPersonToSiteAccesses,
                existingSiteAccesses,
                person,
                permissionLevel);

            // Process all access changes and collect vehicle updates
            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process additions
            var additionsStart = DateTime.UtcNow;
            foreach (var siteAccess in sitesToAdd)
            {
                var newAccess = CreateSiteAccess(siteAccess.SiteId, siteAccess.PermissionId);
                card.SiteVehicleNormalCardAccessItems.Add(newAccess);

                var vehicleAccesses = await AddAccessForDepartmentsOfSiteAsync(
                    card,
                    siteAccess.SiteId,
                    siteAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }
            var additionsDuration = (DateTime.UtcNow - additionsStart).TotalMilliseconds;
            _logger?.LogInformation($"[PERF] Site additions took {additionsDuration}ms for {sitesToAdd.Count} sites");

            // Process removals
            var removalsStart = DateTime.UtcNow;
            foreach (var siteAccess in sitesToRemove)
            {
                // Mark existing access for deletion
                existingSiteAccesses[(siteAccess.SiteId, siteAccess.PermissionId)].IsMarkedForDeletion = true;

                var vehicleAccesses = await RemoveAccessForDepartmentsOfSiteAsync(
                    card,
                    siteAccess.SiteId,
                    siteAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }
            var removalsDuration = (DateTime.UtcNow - removalsStart).TotalMilliseconds;
            _logger?.LogInformation($"[PERF] Site removals took {removalsDuration}ms for {sitesToRemove.Count} sites");

            // Save changes and sync drivers
            var saveStart = DateTime.UtcNow;
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);
            var saveDuration = (DateTime.UtcNow - saveStart).TotalMilliseconds;
            _logger?.LogInformation($"[SIMPLIFIED] PersonDataProvider.SaveAsync took {saveDuration}ms");
            _logger?.LogInformation($"[SIMPLIFIED] UpdateVehicleSiteAccessesForPersonAsync completed for person {personId}");

            // Queue sync operations instead of running them directly
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Use the specific permission level passed from JavaScript
                var permissionLevels = new List<int> { permissionLevel };

                // Send individual message for each vehicle
                for (int i = 0; i < vehicleIds.Count; i++)
                {
                    var syncMessage = new VehicleSyncMessage
                    {
                        VehicleId = vehicleIds[i],
                        PersonId = personId,
                        CustomerId = person.CustomerId,
                        InitiatedByUserId = currentUserId,
                        CreatedAt = DateTime.UtcNow,
                        CorrelationId = correlationId,
                        Priority = "Normal",
                        SyncReason = $"SiteAccessUpdate_Level{permissionLevel}",
                        VehicleSequence = i + 1,
                        TotalVehicles = vehicleIds.Count,
                        PermissionLevels = permissionLevels.Any() ? permissionLevels : null
                    };

                    await vehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                }

                _logger?.LogInformation($"[SIMPLIFIED] {vehicleIds.Count} vehicle sync messages queued for person {personId} (site access update - Level {permissionLevel}) with correlation {correlationId}");
            }

            return new ComponentResponse<bool>(true);
        }

        private async Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> AddAccessForDepartmentsOfSiteAsync(
            CardDataObject card,
            Guid siteId,
            Guid permissionId)
        {
            // Get site with departments in a single query
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = siteId;
            site = await _dataFacade.SiteDataProvider.GetAsync(
                site,
                includes: new List<string> { "DepartmentItems" },
                skipSecurity: true
            ) ?? throw new GOServerException($"Site {siteId} not found");

            var allPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process departments sequentially to avoid MARS issues
            foreach (var department in site.DepartmentItems)
            {
                // Create and add department access
                var deptAccess = _serviceProvider.GetRequiredService<DepartmentVehicleNormalCardAccessDataObject>();
                deptAccess.DepartmentId = department.Id;
                deptAccess.PermissionId = permissionId;
                card.DepartmentVehicleNormalCardAccessItems.Add(deptAccess);

                // Execute operations sequentially
                await AddAccessForModelsOfDepartmentAsync(card, department, permissionId);
                var perVehicleAccesses = await AddAccessForVehiclesOfDepartmentAsync(card, department, permissionId);

                if (perVehicleAccesses.Any())
                {
                    allPerVehicleAccesses.AddRange(perVehicleAccesses);
                }
            }

            return allPerVehicleAccesses;
        }

        private async System.Threading.Tasks.Task AddAccessForModelsOfDepartmentAsync(CardDataObject card, DepartmentDataObject department, Guid permissionId)
        {
            var modelIds = (await department.LoadVehiclesAsync(skipSecurity: true))
                .Select(v => v.ModelId)
                .ToHashSet();

            var existingAccess = card.ModelVehicleNormalCardAccessItems
               .Where(a => a.DepartmentId == department.Id && a.PermissionId == permissionId)
               .Select(a => a.ModelId)
               .ToHashSet();

            var modelsNeedingAccess = modelIds.Except(existingAccess);

            var accessesToAdd = modelsNeedingAccess
                .Select(modelId =>
                {
                    var access = _serviceProvider.GetRequiredService<ModelVehicleNormalCardAccessDataObject>();
                    access.ModelId = modelId;
                    access.PermissionId = permissionId;
                    access.DepartmentId = department.Id;
                    return access;
                })
                .ToList();

            if (accessesToAdd.Any())
            {
                card.ModelVehicleNormalCardAccessItems.AddRange(accessesToAdd);
            }
        }

        private async System.Threading.Tasks.Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> AddAccessForVehiclesOfDepartmentAsync(CardDataObject card, DepartmentDataObject department, Guid permissionId)
        {
            var vehicles = await department.LoadVehiclesAsync(skipSecurity: true);

            // Create all access items at once using LINQ
            var perVehicleAccessList = vehicles
                .Select(vehicle =>
                {
                    var access = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                    access.VehicleId = vehicle.Id;
                    access.PermissionId = permissionId;
                    return access;
                })
                .ToList();

            // Add all items to the card's collection in one batch
            if (perVehicleAccessList.Any())
            {
                card.PerVehicleNormalCardAccessItems.AddRange(perVehicleAccessList);
            }

            return perVehicleAccessList;
        }

        private async System.Threading.Tasks.Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> AddAccessForVehiclesOfModelAsync(CardDataObject card, Guid modelId, Guid permissionId)
        {
            var departments = (await card.LoadDepartmentVehicleNormalCardAccessItemsAsync(skipSecurity: true)).Select(a => a.DepartmentId).ToList();

            var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, $"ModelId == @0 and @1.Contains(outerIt.DepartmentId)", new object[] { modelId, departments }, skipSecurity: true);

            var perVehicleAccessList = new List<PerVehicleNormalCardAccessDataObject>();

            foreach (var vehicle in vehicles)
            {
                if (!card.PerVehicleNormalCardAccessItems.Where(a => a.VehicleId == vehicle.Id && a.PermissionId == a.PermissionId).Any())
                {
                    var perVehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                    perVehicleAccess.VehicleId = vehicle.Id;
                    perVehicleAccess.PermissionId = permissionId;

                    card.PerVehicleNormalCardAccessItems.Add(perVehicleAccess);
                    perVehicleAccessList.Add(perVehicleAccess);
                }
            }

            return perVehicleAccessList;
        }

        private async Task<(List<PersonToSiteVehicleNormalAccessViewDataObject> ToAdd,
                                                List<PersonToSiteVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdates(
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid SiteId, Guid PermissionId), SiteVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person)
        {
            var toAdd = new List<PersonToSiteVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToSiteVehicleNormalAccessViewDataObject>();

            // Create a set of keys from updates for quick lookup
            var updateKeys = new HashSet<(Guid SiteId, Guid PermissionId)>();
            foreach (var update in updates)
            {
                var key = (update.SiteId, update.PermissionId);
                updateKeys.Add(key);

                var hasExisting = existingAccesses.ContainsKey(key);

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                }
            }

            // Load existing view items
            await person.LoadPersonToSiteVehicleNormalAccessViewItemsAsync();

            // Only remove existing accesses for permission levels that are being updated
            // This prevents interference between supervisor (Level 1) and regular (Level 3) access
            var updatedPermissionLevels = updates
                .Select(u => u.PermissionId)
                .Distinct()
                .Select(pid => GetPermissionLevelFromId(pid))
                .Where(level => level.HasValue)
                .Select(level => level.Value)
                .ToHashSet();

            _logger?.LogInformation($"[PERF] Site access - Updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            // Special case: if we have updates with HasAccess=false but no permission levels resolved,
            // we need to determine which permission level we're trying to remove
            var hasRemovalUpdates = updates.Any(u => !u.HasAccess);

            if (hasRemovalUpdates && !updatedPermissionLevels.Any())
            {
                _logger?.LogInformation($"[PERF] Site access - HasRemovalUpdates but no permission levels resolved. Analyzing removal intent...");

                // Try to determine the intended permission level from context
                // Check if we're dealing with a "clear all normal access" scenario
                var existingLevels = existingAccesses.Values
                    .Select(access => GetPermissionLevelFromId(access.PermissionId))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value)
                    .GroupBy(level => level)
                    .ToDictionary(g => g.Key, g => g.Count());

                _logger?.LogInformation($"[PERF] Site access - Existing permission level distribution: {string.Join(", ", existingLevels.Select(kvp => $"Level {kvp.Key}: {kvp.Value} accesses"))}");

                // Analyze what types of accesses we're trying to remove to determine the target level
                var removalPermissionIds = updates.Where(u => !u.HasAccess).Select(u => u.PermissionId).Distinct().ToList();
                var removalLevels = removalPermissionIds
                    .Select(pid => GetPermissionLevelFromId(pid))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value)
                    .Distinct()
                    .ToList();

                if (removalLevels.Any())
                {
                    // If we can determine levels from the removal updates, use those
                    foreach (var level in removalLevels)
                    {
                        updatedPermissionLevels.Add(level);
                    }
                    _logger?.LogInformation($"[PERF] Site access - Determined target levels from removal updates: [{string.Join(", ", removalLevels)}]");
                }
                else
                {
                    // Fallback: analyze existing accesses to determine most likely target
                    // Check if we have supervisor accesses (Level 1) - if so, we might be updating supervisor access
                    // Otherwise, default to normal access (Level 3)
                    if (existingLevels.ContainsKey(1) && existingLevels.ContainsKey(3))
                    {
                        // Both supervisor and normal access exist - be more conservative
                        // Only target the level that has the most accesses matching our removal pattern
                        var level1Count = existingLevels.GetValueOrDefault(1, 0);
                        var level3Count = existingLevels.GetValueOrDefault(3, 0);

                        // Default to Level 3 (normal) unless Level 1 significantly dominates
                        if (level1Count > level3Count * 2)
                        {
                            updatedPermissionLevels.Add(1);
                            _logger?.LogInformation($"[PERF] Site access - Detected supervisor access update intent - targeting Level 1 permissions");
                        }
                        else
                        {
                            updatedPermissionLevels.Add(3);
                            _logger?.LogInformation($"[PERF] Site access - Detected normal access update intent - targeting Level 3 permissions");
                        }
                    }
                    else if (existingLevels.ContainsKey(1))
                    {
                        updatedPermissionLevels.Add(1);
                        _logger?.LogInformation($"[PERF] Site access - Only supervisor access exists - targeting Level 1 permissions");
                    }
                    else if (existingLevels.ContainsKey(3))
                    {
                        updatedPermissionLevels.Add(3);
                        _logger?.LogInformation($"[PERF] Site access - Only normal access exists - targeting Level 3 permissions");
                    }
                }
            }

            _logger?.LogInformation($"[PERF] Site access - Final updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            foreach (var existing in existingAccesses)
            {
                if (!updateKeys.Contains(existing.Key))
                {
                    // Only remove if this existing access belongs to a permission level being updated
                    var existingPermissionLevel = GetPermissionLevelFromId(existing.Value.PermissionId);
                    var shouldRemove = existingPermissionLevel.HasValue && updatedPermissionLevels.Contains(existingPermissionLevel.Value);

                    _logger?.LogInformation($"[PERF] Site access - Found existing access not in updates - SiteId: {existing.Key.SiteId}, PermissionId: {existing.Key.PermissionId}, PermissionLevel: {existingPermissionLevel}, ShouldRemove: {shouldRemove}");

                    if (shouldRemove)
                    {
                        var viewItem = person.PersonToSiteVehicleNormalAccessViewItems.FirstOrDefault(x =>
                            x.SiteId == existing.Key.SiteId &&
                            x.PermissionId == existing.Key.PermissionId);

                        if (viewItem != null)
                        {
                            viewItem.HasAccess = false;
                            toRemove.Add(viewItem);
                            _logger?.LogInformation($"[PERF] Site access - Added existing access to REMOVE list - SiteId: {existing.Key.SiteId}");
                        }
                        else
                        {
                            // Create a new view item for removal since none exists
                            var newViewItem = _serviceProvider.GetRequiredService<PersonToSiteVehicleNormalAccessViewDataObject>();
                            newViewItem.SiteId = existing.Key.SiteId;
                            newViewItem.PermissionId = existing.Key.PermissionId;
                            newViewItem.PersonId = person.Id;
                            newViewItem.HasAccess = false;
                            toRemove.Add(newViewItem);
                            _logger?.LogInformation($"[PERF] Site access - Created new view item for REMOVE list - SiteId: {existing.Key.SiteId}");
                        }
                    }
                    else
                    {
                        _logger?.LogInformation($"[PERF] Site access - Skipping existing access (different permission level) - SiteId: {existing.Key.SiteId}, PermissionLevel: {existingPermissionLevel}");
                    }
                }
            }

            return (toAdd, toRemove);
        }

        private async Task<(List<PersonToDepartmentVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToDepartmentVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdates(
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid DepartmentId, Guid PermissionId), DepartmentVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            HashSet<int> globalPermissionContext)
        {
            var toAdd = new List<PersonToDepartmentVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToDepartmentVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[PERF] CategorizeAccessUpdates for departments - Updates count: {updates.Count}, Existing accesses count: {existingAccesses.Count}");

            // Log all input departments and their HasAccess status
            foreach (var update in updates)
            {
                _logger?.LogInformation($"[PERF] Input department - DepartmentId: {update.DepartmentId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}");
            }

            // Create a set of keys from updates for quick lookup
            var updateKeys = new HashSet<(Guid DepartmentId, Guid PermissionId)>();
            foreach (var update in updates)
            {
                var key = (update.DepartmentId, update.PermissionId);
                updateKeys.Add(key);

                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[PERF] Processing department update - DepartmentId: {update.DepartmentId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[PERF] Added to ADD list - DepartmentId: {update.DepartmentId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[PERF] Added to REMOVE list - DepartmentId: {update.DepartmentId}");
                }
            }

            // Load existing view items
            await person.LoadPersonToDepartmentVehicleNormalAccessViewItemsAsync();

            // Only remove existing accesses for permission levels that are being updated
            // This prevents interference between supervisor (Level 1) and regular (Level 3) access
            var updatedPermissionLevels = updates
                .Select(u => u.PermissionId)
                .Distinct()
                .Select(pid => GetPermissionLevelFromId(pid))
                .Where(level => level.HasValue)
                .Select(level => level.Value)
                .ToHashSet();

            _logger?.LogInformation($"[PERF] Department access - Updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            // Special case: if we have updates with HasAccess=false but no permission levels resolved,
            // we need to determine which permission level we're trying to remove
            var hasRemovalUpdates = updates.Any(u => !u.HasAccess);

            if (hasRemovalUpdates && !updatedPermissionLevels.Any())
            {
                _logger?.LogInformation($"[PERF] Department access - HasRemovalUpdates but no permission levels resolved. Analyzing removal intent...");

                // Try to determine the intended permission level from context
                var existingLevels = existingAccesses.Values
                    .Select(access => GetPermissionLevelFromId(access.PermissionId))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value)
                    .GroupBy(level => level)
                    .ToDictionary(g => g.Key, g => g.Count());

                _logger?.LogInformation($"[PERF] Department access - Existing permission level distribution: {string.Join(", ", existingLevels.Select(kvp => $"Level {kvp.Key}: {kvp.Value} accesses"))}");

                // Analyze what types of accesses we're trying to remove to determine the target level
                var removalPermissionIds = updates.Where(u => !u.HasAccess).Select(u => u.PermissionId).Distinct().ToList();
                var removalLevels = removalPermissionIds
                    .Select(pid => GetPermissionLevelFromId(pid))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value)
                    .Distinct()
                    .ToList();

                if (removalLevels.Any())
                {
                    // If we can determine levels from the removal updates, use those
                    foreach (var level in removalLevels)
                    {
                        updatedPermissionLevels.Add(level);
                    }
                    _logger?.LogInformation($"[PERF] Department access - Determined target levels from removal updates: [{string.Join(", ", removalLevels)}]");
                }
                else
                {
                    // Fallback: analyze existing accesses to determine most likely target
                    // Check if we have supervisor accesses (Level 1) - if so, we might be updating supervisor access
                    // Otherwise, default to normal access (Level 3)
                    if (existingLevels.ContainsKey(1) && existingLevels.ContainsKey(3))
                    {
                        // Both supervisor and normal access exist - be more conservative
                        var level1Count = existingLevels.GetValueOrDefault(1, 0);
                        var level3Count = existingLevels.GetValueOrDefault(3, 0);

                        // Default to Level 3 (normal) unless Level 1 significantly dominates
                        if (level1Count > level3Count * 2)
                        {
                            updatedPermissionLevels.Add(1);
                            _logger?.LogInformation($"[PERF] Department access - Detected supervisor access update intent - targeting Level 1 permissions");
                        }
                        else
                        {
                            updatedPermissionLevels.Add(3);
                            _logger?.LogInformation($"[PERF] Department access - Detected normal access update intent - targeting Level 3 permissions");
                        }
                    }
                    else if (existingLevels.ContainsKey(1))
                    {
                        updatedPermissionLevels.Add(1);
                        _logger?.LogInformation($"[PERF] Department access - Only supervisor access exists - targeting Level 1 permissions");
                    }
                    else if (existingLevels.ContainsKey(3))
                    {
                        updatedPermissionLevels.Add(3);
                        _logger?.LogInformation($"[PERF] Department access - Only normal access exists - targeting Level 3 permissions");
                    }
                }
            }

            _logger?.LogInformation($"[PERF] Department access - Final updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            foreach (var existing in existingAccesses)
            {
                if (!updateKeys.Contains(existing.Key))
                {
                    // Only remove if this existing access belongs to a permission level being updated
                    var existingPermissionLevel = GetPermissionLevelFromId(existing.Value.PermissionId);
                    var shouldRemove = existingPermissionLevel.HasValue && updatedPermissionLevels.Contains(existingPermissionLevel.Value);

                    _logger?.LogInformation($"[PERF] Found existing department access not in updates - DepartmentId: {existing.Key.DepartmentId}, PermissionId: {existing.Key.PermissionId}, PermissionLevel: {existingPermissionLevel}, ShouldRemove: {shouldRemove}");

                    if (shouldRemove)
                    {
                        _logger?.LogInformation($"[PERF] Permission level matches, checking for view item...");

                        var viewItem = person.PersonToDepartmentVehicleNormalAccessViewItems.FirstOrDefault(x =>
                            x.DepartmentId == existing.Key.DepartmentId &&
                            x.PermissionId == existing.Key.PermissionId);

                        if (viewItem != null)
                        {
                            viewItem.HasAccess = false;
                            toRemove.Add(viewItem);
                            _logger?.LogInformation($"[PERF] Added existing department access to REMOVE list - DepartmentId: {existing.Key.DepartmentId}");
                        }
                        else
                        {
                            // Create a new view item for removal since none exists
                            var newViewItem = _serviceProvider.GetRequiredService<PersonToDepartmentVehicleNormalAccessViewDataObject>();
                            newViewItem.DepartmentId = existing.Key.DepartmentId;
                            newViewItem.PermissionId = existing.Key.PermissionId;
                            newViewItem.PersonId = person.Id;
                            newViewItem.HasAccess = false;
                            toRemove.Add(newViewItem);
                            _logger?.LogInformation($"[PERF] Created new view item for REMOVE list - DepartmentId: {existing.Key.DepartmentId}");
                        }
                    }
                    else
                    {
                        _logger?.LogInformation($"[PERF] Skipping existing department access (different permission level) - DepartmentId: {existing.Key.DepartmentId}, PermissionLevel: {existingPermissionLevel}");
                    }
                }
            }

            _logger?.LogInformation($"[PERF] CategorizeAccessUpdates department results - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }

        private async Task<(List<PersonToModelVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToModelVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdates(
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid ModelId, Guid DepartmentId, Guid PermissionId), ModelVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            HashSet<int> globalPermissionContext)
        {
            var toAdd = new List<PersonToModelVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToModelVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[PERF] CategorizeAccessUpdates for models - Updates count: {updates.Count}, Existing accesses count: {existingAccesses.Count}");

            // Log all input models and their HasAccess status
            foreach (var update in updates)
            {
                _logger?.LogInformation($"[PERF] Input model - ModelId: {update.ModelId}, DepartmentId: {update.DepartmentId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}");
            }

            // Create a set of keys from updates for quick lookup
            var updateKeys = new HashSet<(Guid ModelId, Guid DepartmentId, Guid PermissionId)>();
            foreach (var update in updates)
            {
                var key = (update.ModelId, update.DepartmentId, update.PermissionId);
                updateKeys.Add(key);

                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[PERF] Processing model update - ModelId: {update.ModelId}, DepartmentId: {update.DepartmentId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[PERF] Added to ADD list - ModelId: {update.ModelId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[PERF] Added to REMOVE list - ModelId: {update.ModelId}");
                }
            }

            // Load existing view items
            await person.LoadPersonToModelVehicleNormalAccessViewItemsAsync();

            // Only remove existing accesses for permission levels that are being updated
            // This prevents interference between supervisor (Level 1) and regular (Level 3) access
            var updatedPermissionLevels = updates
                .Select(u => u.PermissionId)
                .Distinct()
                .Select(pid => GetPermissionLevelFromId(pid))
                .Where(level => level.HasValue)
                .Select(level => level.Value)
                .ToHashSet();

            _logger?.LogInformation($"[PERF] Updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            // Special case: if we have updates with HasAccess=false but no permission levels resolved,
            // we need to determine which permission level we're trying to remove
            var hasRemovalUpdates = updates.Any(u => !u.HasAccess);

            if (hasRemovalUpdates && !updatedPermissionLevels.Any())
            {
                _logger?.LogInformation($"[PERF] Model access - HasRemovalUpdates but no permission levels resolved. Analyzing removal intent...");
                _logger?.LogInformation($"[PERF] Model access - Global permission context: [{string.Join(", ", globalPermissionContext)}]");

                // Try to determine the intended permission level from context
                var existingLevels = existingAccesses.Values
                    .Select(access => GetPermissionLevelFromId(access.PermissionId))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value)
                    .GroupBy(level => level)
                    .ToDictionary(g => g.Key, g => g.Count());

                _logger?.LogInformation($"[PERF] Model access - Existing permission level distribution: {string.Join(", ", existingLevels.Select(kvp => $"Level {kvp.Key}: {kvp.Value} accesses"))}");

                // Fallback: analyze existing accesses to determine most likely target
                // Check global permission context first - if site/department resolved to Level 1, prefer Level 1
                if (globalPermissionContext.Contains(1))
                {
                    updatedPermissionLevels.Add(1);
                    _logger?.LogInformation($"[PERF] Model access - Using global permission context (Level 1 detected): targeting Level 1 permissions");
                }
                else if (existingLevels.ContainsKey(1) && existingLevels.ContainsKey(3))
                {
                    // Both supervisor and normal access exist - be more conservative
                    var level1Count = existingLevels.GetValueOrDefault(1, 0);
                    var level3Count = existingLevels.GetValueOrDefault(3, 0);

                    // Default to Level 3 (normal) unless Level 1 significantly dominates OR global context suggests Level 1
                    if (level1Count > level3Count * 2 || globalPermissionContext.Contains(1))
                    {
                        updatedPermissionLevels.Add(1);
                        _logger?.LogInformation($"[PERF] Model access - Detected supervisor access update intent - targeting Level 1 permissions");
                    }
                    else
                    {
                        updatedPermissionLevels.Add(3);
                        _logger?.LogInformation($"[PERF] Model access - Detected normal access update intent - targeting Level 3 permissions");
                    }
                }
                else if (existingLevels.ContainsKey(1))
                {
                    updatedPermissionLevels.Add(1);
                    _logger?.LogInformation($"[PERF] Model access - Only supervisor access exists - targeting Level 1 permissions");
                }
                else if (existingLevels.ContainsKey(3))
                {
                    updatedPermissionLevels.Add(3);
                    _logger?.LogInformation($"[PERF] Model access - Only normal access exists - targeting Level 3 permissions");
                }
                else
                {
                    // Final fallback - default to Level 3 if no context available
                    updatedPermissionLevels.Add(3);
                    _logger?.LogInformation($"[PERF] Model access - No existing accesses found, defaulting to Level 3 permissions");
                }
            }

            _logger?.LogInformation($"[PERF] Final updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            foreach (var existing in existingAccesses)
            {
                if (!updateKeys.Contains(existing.Key))
                {
                    // Only remove if this existing access belongs to a permission level being updated
                    var existingPermissionLevel = GetPermissionLevelFromId(existing.Value.PermissionId);
                    var shouldRemove = existingPermissionLevel.HasValue && updatedPermissionLevels.Contains(existingPermissionLevel.Value);

                    _logger?.LogInformation($"[PERF] Found existing model access not in updates - ModelId: {existing.Key.ModelId}, DepartmentId: {existing.Key.DepartmentId}, PermissionId: {existing.Key.PermissionId}, PermissionLevel: {existingPermissionLevel}, ShouldRemove: {shouldRemove}");

                    if (shouldRemove)
                    {
                        _logger?.LogInformation($"[PERF] Permission level matches, checking for view item...");

                        var viewItem = person.PersonToModelVehicleNormalAccessViewItems.FirstOrDefault(x =>
                            x.ModelId == existing.Key.ModelId &&
                            x.DepartmentId == existing.Key.DepartmentId &&
                            x.PermissionId == existing.Key.PermissionId);

                        if (viewItem != null)
                        {
                            viewItem.HasAccess = false;
                            toRemove.Add(viewItem);
                            _logger?.LogInformation($"[PERF] Added existing model access to REMOVE list - ModelId: {existing.Key.ModelId}");
                        }
                        else
                        {
                            // Create a new view item for removal since none exists
                            var newViewItem = _serviceProvider.GetRequiredService<PersonToModelVehicleNormalAccessViewDataObject>();
                            newViewItem.ModelId = existing.Key.ModelId;
                            newViewItem.DepartmentId = existing.Key.DepartmentId;
                            newViewItem.PermissionId = existing.Key.PermissionId;
                            newViewItem.PersonId = person.Id;
                            newViewItem.HasAccess = false;
                            toRemove.Add(newViewItem);
                            _logger?.LogInformation($"[PERF] Created new view item for REMOVE list - ModelId: {existing.Key.ModelId}");
                        }
                    }
                    else
                    {
                        _logger?.LogInformation($"[PERF] Skipping existing model access (different permission level) - ModelId: {existing.Key.ModelId}, PermissionLevel: {existingPermissionLevel}");
                    }
                }
            }

            _logger?.LogInformation($"[PERF] CategorizeAccessUpdates model results - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }

        private async Task<(List<PersonToPerVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToPerVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdates(
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid VehicleId, Guid PermissionId), PerVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            HashSet<int> globalPermissionContext)
        {
            var toAdd = new List<PersonToPerVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToPerVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[PERF] CategorizeAccessUpdates for vehicles - Updates count: {updates.Count}, Existing accesses count: {existingAccesses.Count}");

            // Log all input vehicles and their HasAccess status
            foreach (var update in updates)
            {
                _logger?.LogInformation($"[PERF] Input vehicle - VehicleId: {update.VehicleId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HireNo: {update.HireNo}");
            }

            // Log all existing accesses
            foreach (var existing in existingAccesses)
            {
                _logger?.LogInformation($"[PERF] Existing access - VehicleId: {existing.Key.VehicleId}, PermissionId: {existing.Key.PermissionId}");
            }

            // Create a set of keys from updates for quick lookup
            var updateKeys = new HashSet<(Guid VehicleId, Guid PermissionId)>();
            foreach (var update in updates)
            {
                var key = (update.VehicleId, update.PermissionId);
                updateKeys.Add(key);

                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[PERF] Processing update - VehicleId: {update.VehicleId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[PERF] Added to ADD list - VehicleId: {update.VehicleId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[PERF] Added to REMOVE list - VehicleId: {update.VehicleId}");
                }
            }

            // Load existing view items
            await person.LoadPersonToPerVehicleNormalAccessViewItemsAsync();

            // Only remove existing accesses for permission levels that are being updated
            // This prevents interference between supervisor (Level 1) and regular (Level 3) access
            var updatedPermissionLevels = updates
                .Select(u => u.PermissionId)
                .Distinct()
                .Select(pid => GetPermissionLevelFromId(pid))
                .Where(level => level.HasValue)
                .Select(level => level.Value)
                .ToHashSet();

            _logger?.LogInformation($"[PERF] Updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            // Special case: if we have updates with HasAccess=false but no permission levels resolved,
            // we need to determine which permission level we're trying to remove
            // Look at the typical permission IDs to determine the intended permission level
            var hasRemovalUpdates = updates.Any(u => !u.HasAccess);

            if (hasRemovalUpdates && !updatedPermissionLevels.Any())
            {
                _logger?.LogInformation($"[PERF] HasRemovalUpdates but no permission levels resolved. Analyzing removal intent...");
                _logger?.LogInformation($"[PERF] Vehicle access - Global permission context: [{string.Join(", ", globalPermissionContext)}]");

                // Try to determine the intended permission level from context
                // Check if we're dealing with a "clear all normal access" scenario
                // by looking at the most common permission levels in existing accesses
                var existingLevels = existingAccesses.Values
                    .Select(access => GetPermissionLevelFromId(access.PermissionId))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value)
                    .GroupBy(level => level)
                    .ToDictionary(g => g.Key, g => g.Count());

                _logger?.LogInformation($"[PERF] Existing permission level distribution: {string.Join(", ", existingLevels.Select(kvp => $"Level {kvp.Key}: {kvp.Value} accesses"))}");

                // Fallback: analyze existing accesses to determine most likely target
                // Check global permission context first - if site/department/model resolved to Level 1, prefer Level 1
                if (globalPermissionContext.Contains(1))
                {
                    updatedPermissionLevels.Add(1);
                    _logger?.LogInformation($"[PERF] Vehicle access - Using global permission context (Level 1 detected): targeting Level 1 permissions");
                }
                else if (existingLevels.ContainsKey(1) && existingLevels.ContainsKey(3))
                {
                    // Both supervisor and normal access exist - be more conservative
                    var level1Count = existingLevels.GetValueOrDefault(1, 0);
                    var level3Count = existingLevels.GetValueOrDefault(3, 0);

                    // Default to Level 3 (normal) unless Level 1 significantly dominates OR global context suggests Level 1
                    if (level1Count > level3Count * 2 || globalPermissionContext.Contains(1))
                    {
                        updatedPermissionLevels.Add(1);
                        _logger?.LogInformation($"[PERF] Vehicle access - Detected supervisor access update intent - targeting Level 1 permissions");
                    }
                    else
                    {
                        updatedPermissionLevels.Add(3);
                        _logger?.LogInformation($"[PERF] Vehicle access - Detected normal access update intent - targeting Level 3 permissions");
                    }
                }
                else if (existingLevels.ContainsKey(1))
                {
                    updatedPermissionLevels.Add(1);
                    _logger?.LogInformation($"[PERF] Vehicle access - Only supervisor access exists - targeting Level 1 permissions");
                }
                else if (existingLevels.ContainsKey(3))
                {
                    updatedPermissionLevels.Add(3);
                    _logger?.LogInformation($"[PERF] Vehicle access - Only normal access exists - targeting Level 3 permissions");
                }
                else
                {
                    // Final fallback - default to Level 3 if no context available
                    updatedPermissionLevels.Add(3);
                    _logger?.LogInformation($"[PERF] Vehicle access - No existing accesses found, defaulting to Level 3 permissions");
                }
            }

            _logger?.LogInformation($"[PERF] Final updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            foreach (var existing in existingAccesses)
            {
                if (!updateKeys.Contains(existing.Key))
                {
                    // Only remove if this existing access belongs to a permission level being updated
                    var existingPermissionLevel = GetPermissionLevelFromId(existing.Value.PermissionId);
                    var shouldRemove = existingPermissionLevel.HasValue && updatedPermissionLevels.Contains(existingPermissionLevel.Value);

                    _logger?.LogInformation($"[PERF] Found existing access not in updates - VehicleId: {existing.Key.VehicleId}, PermissionId: {existing.Key.PermissionId}, PermissionLevel: {existingPermissionLevel}, ShouldRemove: {shouldRemove}");

                    if (shouldRemove)
                    {
                        _logger?.LogInformation($"[PERF] Permission level matches, checking for view item...");

                        var viewItem = person.PersonToPerVehicleNormalAccessViewItems.FirstOrDefault(x =>
                            x.VehicleId == existing.Key.VehicleId &&
                            x.PermissionId == existing.Key.PermissionId);

                        if (viewItem != null)
                        {
                            viewItem.HasAccess = false;
                            toRemove.Add(viewItem);
                            _logger?.LogInformation($"[PERF] Added existing access to REMOVE list - VehicleId: {existing.Key.VehicleId}");
                        }
                        else
                        {
                            // Create a new view item for removal since none exists
                            var newViewItem = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewDataObject>();
                            newViewItem.VehicleId = existing.Key.VehicleId;
                            newViewItem.PermissionId = existing.Key.PermissionId;
                            newViewItem.PersonId = person.Id;
                            newViewItem.HasAccess = false;
                            toRemove.Add(newViewItem);
                            _logger?.LogInformation($"[PERF] Created new view item for REMOVE list - VehicleId: {existing.Key.VehicleId}");
                        }
                    }
                    else
                    {
                        _logger?.LogInformation($"[PERF] Skipping existing access (different permission level) - VehicleId: {existing.Key.VehicleId}, PermissionLevel: {existingPermissionLevel}");
                    }
                }
            }

            _logger?.LogInformation($"[PERF] CategorizeAccessUpdates results - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }

        private DepartmentVehicleNormalCardAccessDataObject CreateDepartmentAccess(
            Guid departmentId,
            Guid permissionId)
        {
            var deptAccess = _serviceProvider.GetRequiredService<DepartmentVehicleNormalCardAccessDataObject>();
            deptAccess.DepartmentId = departmentId;
            deptAccess.PermissionId = permissionId;
            return deptAccess;
        }

        private ModelVehicleNormalCardAccessDataObject CreateModelAccess(
            Guid modelId,
            Guid departmentId,
            Guid permissionId)
        {
            var modelAccess = _serviceProvider.GetRequiredService<ModelVehicleNormalCardAccessDataObject>();
            modelAccess.ModelId = modelId;
            modelAccess.DepartmentId = departmentId;
            modelAccess.PermissionId = permissionId;
            return modelAccess;
        }

        private SiteVehicleNormalCardAccessDataObject CreateSiteAccess(Guid siteId, Guid permissionId)
        {
            var siteAccess = _serviceProvider.GetRequiredService<SiteVehicleNormalCardAccessDataObject>();
            siteAccess.SiteId = siteId;
            siteAccess.PermissionId = permissionId;
            return siteAccess;
        }
        private PerVehicleNormalCardAccessDataObject CreateVehicleAccess(
            Guid vehicleId,
            Guid permissionId)
        {
            var vehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
            vehicleAccess.VehicleId = vehicleId;
            vehicleAccess.PermissionId = permissionId;
            return vehicleAccess;
        }

        private async Task<DepartmentDataObject> GetDepartmentAsync(Guid departmentId)
        {
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = departmentId;
            return await _dataFacade.DepartmentDataProvider.GetAsync(department, skipSecurity: true)
                ?? throw new GOServerException($"Department {departmentId} not found");
        }
        private async System.Threading.Tasks.Task<PersonDataObject> GetPersonAsync(Guid personId)
        {
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = personId;

            person = await _dataFacade.PersonDataProvider.GetAsync(person, includes: new List<string> {
                "Driver.Card",
                    "Driver.Card.SiteVehicleNormalCardAccessItems",
                    "Driver.Card.DepartmentVehicleNormalCardAccessItems",
                    "Driver.Card.ModelVehicleNormalCardAccessItems",
                    "Driver.Card.PerVehicleNormalCardAccessItems" }, skipSecurity: true);

            if (person == null || person.Driver == null || person.Driver.Card == null)
            {
                throw new GOServerException("Something went wrong. Input should be a person who is a driver and has an access card. One of those is not happening");
            }

            return person;
        }

        private async Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> RemoveAccessForDepartmentsOfSiteAsync(
            CardDataObject card,
            Guid siteId,
            Guid permissionId)
        {
            // Load all department access items first
            var deptAccessItems = await card.LoadDepartmentVehicleNormalCardAccessItemsAsync(skipSecurity: true);

            // Filter by permissionId first to reduce unnecessary department loads
            var relevantAccesses = deptAccessItems
                .Where(access => access.PermissionId == permissionId)
                .ToList();

            if (!relevantAccesses.Any())
            {
                return Enumerable.Empty<PerVehicleNormalCardAccessDataObject>();
            }

            var perVehicleAccessList = new List<PerVehicleNormalCardAccessDataObject>();

            // Process relevant accesses sequentially
            foreach (var deptAccess in relevantAccesses)
            {
                var department = await deptAccess.LoadDepartmentAsync(skipSecurity: true);

                if (department.SiteId == siteId)
                {
                    // Mark for deletion
                    deptAccess.IsMarkedForDeletion = true;

                    // Remove vehicle access first as it depends on department existence
                    var perVehicleAccesses = await RemoveAccessForVehiclesOfDepartmentAsync(
                        card,
                        deptAccess.DepartmentId,
                        permissionId);

                    if (perVehicleAccesses.Any())
                    {
                        perVehicleAccessList.AddRange(perVehicleAccesses);
                    }

                    // Remove model access last as it might reference vehicles
                    await RemoveAccessForModelsOfDepartmentAsync(
                        card,
                        department,
                        permissionId);
                }
            }

            return perVehicleAccessList;
        }
        private async Task RemoveAccessForModelsOfDepartmentAsync(
            CardDataObject card,
            DepartmentDataObject department,
            Guid permissionId)
        {
            // Load data sequentially to avoid MARS issues
            var site = await department.LoadSiteAsync();
            await site.LoadDepartmentItemsAsync(skipSecurity: true);

            // Get current department's models
            var currentDeptVehicles = await department.LoadVehiclesAsync(skipSecurity: true);
            var currentDeptModelIds = currentDeptVehicles
                .Select(v => v.ModelId)
                .ToHashSet();

            // Load other departments' vehicles sequentially
            var otherDeptModelIds = new HashSet<Guid>();
            foreach (var otherDept in site.DepartmentItems.Where(d => d.Id != department.Id))
            {
                var vehicles = await otherDept.LoadVehiclesAsync(skipSecurity: true);
                foreach (var vehicle in vehicles)
                {
                    otherDeptModelIds.Add(vehicle.ModelId);
                }
            }

            // Find models unique to current department
            var uniqueModelIds = currentDeptModelIds.Except(otherDeptModelIds);

            // Create lookup for existing access items
            var existingAccesses = card.ModelVehicleNormalCardAccessItems
                .Where(a => a.DepartmentId == department.Id && a.PermissionId == permissionId)
                .ToLookup(a => a.ModelId);

            // Mark items for deletion
            foreach (var modelId in uniqueModelIds)
            {
                var access = existingAccesses[modelId].SingleOrDefault();
                if (access != null)
                {
                    access.IsMarkedForDeletion = true;
                }
            }
        }

        private async Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> RemoveAccessForVehiclesOfDepartmentAsync(
            CardDataObject card,
            Guid departmentId,
            Guid permissionId)
        {
            // Load all vehicle access items in one go
            var perVehicleAccessList = await card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);

            // Filter to only relevant items based on permissionId first
            var relevantAccesses = perVehicleAccessList
                .Where(access => access.PermissionId == permissionId)
                .ToList();

            if (!relevantAccesses.Any())
            {
                return perVehicleAccessList;
            }

            // Load vehicles sequentially to avoid MARS issues
            foreach (var access in relevantAccesses)
            {
                var vehicle = await access.LoadVehicleAsync(skipSecurity: true);
                if (vehicle.DepartmentId == departmentId)
                {
                    access.IsMarkedForDeletion = true;
                }
            }

            return perVehicleAccessList;
        }

        private async System.Threading.Tasks.Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> RemoveAccessForVehiclesOfModelAsync(CardDataObject card, Guid modelId, Guid permissionId)
        {
            var allAccessItems = await card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);
            var perVehicleAccessList = new List<PerVehicleNormalCardAccessDataObject>(); // Assuming AccessItem is the type of the items in the collection

            foreach (var accessItem in allAccessItems)
            {
                var vehicle = await accessItem.LoadVehicleAsync(skipSecurity: true);
                if (vehicle.ModelId == modelId && accessItem.PermissionId == permissionId)
                {
                    perVehicleAccessList.Add(accessItem);
                }
            }

            foreach (var perVehicleAccess in perVehicleAccessList)
            {
                perVehicleAccess.IsMarkedForDeletion = true;
            }

            return perVehicleAccessList;
        }

        /// <summary>
        /// Helper method to get permission level from permission ID
        /// Uses a simple cache to avoid repeated database calls
        /// </summary>
        private static readonly Dictionary<Guid, int?> _permissionLevelCache = new Dictionary<Guid, int?>();
        private static readonly object _permissionLevelCacheLock = new object();

        private int? GetPermissionLevelFromId(Guid permissionId)
        {
            lock (_permissionLevelCacheLock)
            {
                if (_permissionLevelCache.TryGetValue(permissionId, out var cachedLevel))
                {
                    return cachedLevel;
                }

                try
                {
                    // Since this is called from async context but we need to avoid deadlock,
                    // we'll use a simple pattern that works within the current async flow
                    var permission = _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { permissionId }, skipSecurity: true).Result?.FirstOrDefault();
                    var level = permission?.LevelName;
                    var levelInt = level.HasValue ? (int)level.Value : (int?)null;
                    _permissionLevelCache[permissionId] = levelInt;
                    return levelInt;
                }
                catch
                {
                    _permissionLevelCache[permissionId] = null;
                    return null;
                }
            }
        }

        private async Task SyncDriversInScopeAsync(List<Guid> vehicleIds, IDataFacade scopedDataFacade, IDeviceTwinHandler scopedDeviceTwinHandler, IServiceProvider serviceProvider, Guid? userId = null)
        {
            _logger?.LogInformation($"[PERF] SyncDriversInScopeAsync called with {vehicleIds.Count} vehicle IDs");
            if (vehicleIds.Any())
            {
                var iotDevices = new List<string>();

                if (userId == null)
                {
                    return;
                }

                foreach (var vehicleId in vehicleIds)
                {
                    try
                    {
                        // Reload vehicle data in the new scope
                        var vehicle = serviceProvider.GetRequiredService<VehicleDataObject>();
                        vehicle.Id = vehicleId;
                        vehicle = await scopedDataFacade.VehicleDataProvider.GetAsync(vehicle, skipSecurity: true);

                        if (vehicle != null)
                        {
                            var module = await vehicle.LoadModuleAsync(skipSecurity: true);
                            if (module != null && !string.IsNullOrEmpty(module.IoTDevice))
                            {
                                if (!iotDevices.Contains(module.IoTDevice))
                                {
                                    iotDevices.Add(module.IoTDevice);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, $"Failed to load vehicle {vehicleId} for sync: {ex.Message}");
                    }
                }

                if (iotDevices.Any())
                {
                    foreach (var iotDevice in iotDevices)
                    {
                        try
                        {
                            _logger?.LogInformation($"Syncing driver to vehicle {iotDevice} for user {userId}");
                            await scopedDeviceTwinHandler.SyncDriverToVehicle(iotDevice, userId.Value);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, $"Failed to sync device {iotDevice}: {ex.Message}");
                        }
                    }
                }
            }
        }

        // SIMPLIFIED: New method that doesn't guess permission levels
        private async Task<(List<PersonToDepartmentVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToDepartmentVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdatesSimplified(
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid DepartmentId, Guid PermissionId), DepartmentVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            int targetPermissionLevel)
        {
            var toAdd = new List<PersonToDepartmentVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToDepartmentVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[SIMPLIFIED] CategorizeAccessUpdatesSimplified for departments - Target permission level: {targetPermissionLevel}");

            // Load existing view items
            await person.LoadPersonToDepartmentVehicleNormalAccessViewItemsAsync();

            // Process updates - only affect the target permission level
            foreach (var update in updates)
            {
                var updatePermissionLevel = GetPermissionLevelFromId(update.PermissionId);
                if (updatePermissionLevel != targetPermissionLevel)
                {
                    _logger?.LogInformation($"[SIMPLIFIED] Skipping update with permission level {updatePermissionLevel}, target is {targetPermissionLevel}");
                    continue;
                }

                var key = (update.DepartmentId, update.PermissionId);
                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[SIMPLIFIED] Processing department - DepartmentId: {update.DepartmentId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to ADD list - DepartmentId: {update.DepartmentId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to REMOVE list - DepartmentId: {update.DepartmentId}");
                }
            }

            // Only remove existing accesses that match the target permission level
            var existingViewItems = person.PersonToDepartmentVehicleNormalAccessViewItems?.ToList() ?? new List<PersonToDepartmentVehicleNormalAccessViewDataObject>();
            var updateKeys = new HashSet<(Guid DepartmentId, Guid PermissionId)>(updates.Select(u => (u.DepartmentId, u.PermissionId)));

            foreach (var existingItem in existingViewItems)
            {
                var existingPermissionLevel = GetPermissionLevelFromId(existingItem.PermissionId);
                if (existingPermissionLevel == targetPermissionLevel)
                {
                    var key = (existingItem.DepartmentId, existingItem.PermissionId);

                    // If this existing access is not in our updates, it should be removed
                    if (!updateKeys.Contains(key))
                    {
                        var removeUpdate = _serviceProvider.GetRequiredService<PersonToDepartmentVehicleNormalAccessViewDataObject>();
                        removeUpdate.DepartmentId = existingItem.DepartmentId;
                        removeUpdate.PermissionId = existingItem.PermissionId;
                        removeUpdate.HasAccess = false;

                        toRemove.Add(removeUpdate);
                        _logger?.LogInformation($"[SIMPLIFIED] Auto-removing existing access - DepartmentId: {existingItem.DepartmentId}, PermissionId: {existingItem.PermissionId}");
                    }
                }
            }

            _logger?.LogInformation($"[SIMPLIFIED] Final result - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }

        // SIMPLIFIED: Model access updates without permission guessing
        private async Task<(List<PersonToModelVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToModelVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeModelAccessUpdatesSimplified(
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid ModelId, Guid DepartmentId, Guid PermissionId), ModelVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            int targetPermissionLevel)
        {
            var toAdd = new List<PersonToModelVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToModelVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[SIMPLIFIED] CategorizeModelAccessUpdatesSimplified - Target permission level: {targetPermissionLevel}");

            // Load existing view items
            await person.LoadPersonToModelVehicleNormalAccessViewItemsAsync();

            // Process updates - only affect the target permission level
            foreach (var update in updates)
            {
                var updatePermissionLevel = GetPermissionLevelFromId(update.PermissionId);
                if (updatePermissionLevel != targetPermissionLevel)
                {
                    _logger?.LogInformation($"[SIMPLIFIED] Skipping model update with permission level {updatePermissionLevel}, target is {targetPermissionLevel}");
                    continue;
                }

                var key = (update.ModelId, update.DepartmentId, update.PermissionId);
                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[SIMPLIFIED] Processing model - ModelId: {update.ModelId}, DepartmentId: {update.DepartmentId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to ADD list - ModelId: {update.ModelId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to REMOVE list - ModelId: {update.ModelId}");
                }
            }

            // Only remove existing accesses that match the target permission level
            var existingViewItems = person.PersonToModelVehicleNormalAccessViewItems?.ToList() ?? new List<PersonToModelVehicleNormalAccessViewDataObject>();
            var updateKeys = new HashSet<(Guid ModelId, Guid DepartmentId, Guid PermissionId)>(updates.Select(u => (u.ModelId, u.DepartmentId, u.PermissionId)));

            foreach (var existingItem in existingViewItems)
            {
                var existingPermissionLevel = GetPermissionLevelFromId(existingItem.PermissionId);
                if (existingPermissionLevel == targetPermissionLevel)
                {
                    var key = (existingItem.ModelId, existingItem.DepartmentId, existingItem.PermissionId);

                    if (!updateKeys.Contains(key))
                    {
                        var removeUpdate = _serviceProvider.GetRequiredService<PersonToModelVehicleNormalAccessViewDataObject>();
                        removeUpdate.ModelId = existingItem.ModelId;
                        removeUpdate.DepartmentId = existingItem.DepartmentId;
                        removeUpdate.PermissionId = existingItem.PermissionId;
                        removeUpdate.HasAccess = false;

                        toRemove.Add(removeUpdate);
                        _logger?.LogInformation($"[SIMPLIFIED] Auto-removing existing model access - ModelId: {existingItem.ModelId}, DepartmentId: {existingItem.DepartmentId}");
                    }
                }
            }

            _logger?.LogInformation($"[SIMPLIFIED] Model access final result - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }

        // SIMPLIFIED: Vehicle access updates without permission guessing
        private async Task<(List<PersonToPerVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToPerVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeVehicleAccessUpdatesSimplified(
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid VehicleId, Guid PermissionId), PerVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            int targetPermissionLevel)
        {
            var toAdd = new List<PersonToPerVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToPerVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[SIMPLIFIED] CategorizeVehicleAccessUpdatesSimplified - Target permission level: {targetPermissionLevel}");

            // Load existing view items
            await person.LoadPersonToPerVehicleNormalAccessViewItemsAsync();

            // Process updates - only affect the target permission level
            foreach (var update in updates)
            {
                var updatePermissionLevel = GetPermissionLevelFromId(update.PermissionId);
                if (updatePermissionLevel != targetPermissionLevel)
                {
                    _logger?.LogInformation($"[SIMPLIFIED] Skipping vehicle update with permission level {updatePermissionLevel}, target is {targetPermissionLevel}");
                    continue;
                }

                var key = (update.VehicleId, update.PermissionId);
                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[SIMPLIFIED] Processing vehicle - VehicleId: {update.VehicleId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to ADD list - VehicleId: {update.VehicleId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to REMOVE list - VehicleId: {update.VehicleId}");
                }
            }

            // Only remove existing accesses that match the target permission level
            var existingViewItems = person.PersonToPerVehicleNormalAccessViewItems?.ToList() ?? new List<PersonToPerVehicleNormalAccessViewDataObject>();
            var updateKeys = new HashSet<(Guid VehicleId, Guid PermissionId)>(updates.Select(u => (u.VehicleId, u.PermissionId)));

            foreach (var existingItem in existingViewItems)
            {
                var existingPermissionLevel = GetPermissionLevelFromId(existingItem.PermissionId);
                if (existingPermissionLevel == targetPermissionLevel)
                {
                    var key = (existingItem.VehicleId, existingItem.PermissionId);

                    if (!updateKeys.Contains(key))
                    {
                        var removeUpdate = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewDataObject>();
                        removeUpdate.VehicleId = existingItem.VehicleId;
                        removeUpdate.PermissionId = existingItem.PermissionId;
                        removeUpdate.HasAccess = false;

                        toRemove.Add(removeUpdate);
                        _logger?.LogInformation($"[SIMPLIFIED] Auto-removing existing vehicle access - VehicleId: {existingItem.VehicleId}");
                    }
                }
            }

            _logger?.LogInformation($"[SIMPLIFIED] Vehicle access final result - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }

        // SIMPLIFIED: Site access updates without permission guessing
        private async Task<(List<PersonToSiteVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToSiteVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeSiteAccessUpdatesSimplified(
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid SiteId, Guid PermissionId), SiteVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            int targetPermissionLevel)
        {
            var toAdd = new List<PersonToSiteVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToSiteVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[SIMPLIFIED] CategorizeSiteAccessUpdatesSimplified - Target permission level: {targetPermissionLevel}");

            // Load existing view items
            await person.LoadPersonToSiteVehicleNormalAccessViewItemsAsync();

            // Process updates - only affect the target permission level
            foreach (var update in updates)
            {
                var updatePermissionLevel = GetPermissionLevelFromId(update.PermissionId);
                if (updatePermissionLevel != targetPermissionLevel)
                {
                    _logger?.LogInformation($"[SIMPLIFIED] Skipping site update with permission level {updatePermissionLevel}, target is {targetPermissionLevel}");
                    continue;
                }

                var key = (update.SiteId, update.PermissionId);
                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[SIMPLIFIED] Processing site - SiteId: {update.SiteId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to ADD list - SiteId: {update.SiteId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to REMOVE list - SiteId: {update.SiteId}");
                }
            }

            // Only remove existing accesses that match the target permission level
            var existingViewItems = person.PersonToSiteVehicleNormalAccessViewItems?.ToList() ?? new List<PersonToSiteVehicleNormalAccessViewDataObject>();
            var updateKeys = new HashSet<(Guid SiteId, Guid PermissionId)>(updates.Select(u => (u.SiteId, u.PermissionId)));

            foreach (var existingItem in existingViewItems)
            {
                var existingPermissionLevel = GetPermissionLevelFromId(existingItem.PermissionId);
                if (existingPermissionLevel == targetPermissionLevel)
                {
                    var key = (existingItem.SiteId, existingItem.PermissionId);

                    if (!updateKeys.Contains(key))
                    {
                        var removeUpdate = _serviceProvider.GetRequiredService<PersonToSiteVehicleNormalAccessViewDataObject>();
                        removeUpdate.SiteId = existingItem.SiteId;
                        removeUpdate.PermissionId = existingItem.PermissionId;
                        removeUpdate.HasAccess = false;

                        toRemove.Add(removeUpdate);
                        _logger?.LogInformation($"[SIMPLIFIED] Auto-removing existing site access - SiteId: {existingItem.SiteId}");
                    }
                }
            }

            _logger?.LogInformation($"[SIMPLIFIED] Site access final result - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }
    }
}
